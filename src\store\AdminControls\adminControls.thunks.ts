import api from '@/api';
import { AdminLoginDataType, AdminLoginRequest, ParentLoginDataType, ParentLoginRequest } from '@/types/AdminControls';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchAdminLoginList = createAsyncThunk<AdminLoginDataType[], AdminLoginRequest, { rejectValue: string }>(
  'adminControls/mangeLoginList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.AdminControls.GetAdminLoginList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Manage Login List');
    }
  }
);

export const fetchParentLoginList = createAsyncThunk<
  ParentLoginDataType[],
  ParentLoginRequest,
  { rejectValue: string }
>('adminControls/parentLoginList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.AdminControls.GetParentLoginList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Parent Login List');
  }
});
