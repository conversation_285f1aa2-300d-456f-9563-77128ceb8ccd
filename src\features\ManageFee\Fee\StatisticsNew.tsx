import React from 'react';
import { Box, Card } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { OverViewProps } from '@/types/Common';
import AreaChartComponent from './AreaChartFeeOverview';

const StatisticRoot = styled.div`
  width: 100%;
  .apexcharts-menu-icon {
    display: none;
  }
`;

function Statistic({ academicId, feeTypeId }: OverViewProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';

  return (
    <StatisticRoot>
      <Card
        sx={{
          boxShadow: 0,
          height: '245px',
          backgroundColor: isLight ? theme.palette.chart.violet[5] : theme.palette.grey[900],
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <svg
          style={{
            position: 'absolute',
            top: -85,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: -1,
          }}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1440 320"
        >
          <path
            fill={isLight ? theme.palette.chart.violet[4] : theme.palette.grey[500]}
            fillOpacity="0.1"
            d="M0,32L24,42.7C48,53,96,75,144,85.3C192,96,240,96,288,117.3C336,139,384,181,432,181.3C480,181,528,139,576,106.7C624,75,672,53,720,53.3C768,53,816,75,864,90.7C912,107,960,117,1008,144C1056,171,1104,213,1152,197.3C1200,181,1248,107,1296,96C1344,85,1392,139,1416,165.3L1440,192L1440,0L0,0Z"
          />
          <path fill={isLight ? theme.palette.chart.violet[4] : theme.palette.grey[500]} fillOpacity="0.1">
            <animate
              attributeName="d"
              dur="8s"
              repeatCount="indefinite"
              values="
                M0,160L24,165.3C48,171,96,181,144,197.3C192,213,240,235,288,208C336,181,384,107,432,106.7C480,107,528,181,576,218.7C624,256,672,256,720,234.7C768,213,816,171,864,144C912,117,960,107,1008,101.3C1056,96,1104,96,1152,128C1200,160,1248,224,1296,245.3C1344,267,1392,245,1416,234.7L1440,224L1440,0L0,0Z;
                M0,150L24,160C48,170,96,180,144,200C192,220,240,240,288,210C336,180,384,120,432,115C480,110,528,180,576,210C624,240,672,240,720,220C768,200,816,150,864,130C912,110,960,100,1008,105C1056,110,1104,110,1152,140C1200,170,1248,220,1296,240C1344,260,1392,240,1416,230L1440,220L1440,0L0,0Z;
                M0,160L24,165.3C48,171,96,181,144,197.3C192,213,240,235,288,208C336,181,384,107,432,106.7C480,107,528,181,576,218.7C624,256,672,256,720,234.7C768,213,816,171,864,144C912,117,960,107,1008,101.3C1056,96,1104,96,1152,128C1200,160,1248,224,1296,245.3C1344,267,1392,245,1416,234.7L1440,224L1440,0L0,0Z
              "
            />
          </path>
        </svg>

        <Box sx={{ p: 2 }}>
          <AreaChartComponent academicId={academicId} feeTypeId={feeTypeId} />
        </Box>
      </Card>
    </StatisticRoot>
  );
}

export default Statistic;
