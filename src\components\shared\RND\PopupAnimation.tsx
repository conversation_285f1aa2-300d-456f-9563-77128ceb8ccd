import { useState } from 'react';
import { Card, CardMedia, CardContent, Typography, Box, styled } from '@mui/material';

// ---------------------- Styled Components ----------------------

const VideoCardWrapper = styled(Card)(({ theme }) => ({
  cursor: 'pointer',
  borderRadius: 12,
  overflow: 'hidden',
  transition: 'transform 0.25s ease, box-shadow 0.25s ease',
  '&:hover': {
    transform: 'scale(1.04)',
    boxShadow: theme.shadows[6],
  },
}));

const Backdrop = styled(Box)(({ open }) => ({
  display: open ? 'block' : 'none',
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100vw',
  height: '100vh',
  backgroundColor: 'rgba(0,0,0,0.6)',
  zIndex: 1200,
}));

const PopupCard = styled(Box)(({ open }) => ({
  position: 'fixed',
  top: open ? '50%' : '45%',
  left: '50%',
  transform: open ? 'translate(-50%, -50%) scale(1)' : 'translate(-50%, -50%) scale(0.5)',
  opacity: open ? 1 : 0,
  transition: 'all 0.35s ease',
  width: '90%',
  maxWidth: 480,
  borderRadius: 12,
  backgroundColor: '#fff',
  overflow: 'hidden',
  zIndex: 1300,
}));

// ---------------------- Main Component ----------------------

export default function VideoCardPopup() {
  const [open, setOpen] = useState(false);
  const [activeVideo, setActiveVideo] = useState<any>(null);

  const videos = [
    {
      id: 1,
      title: 'React Basics Tutorial',
      thumbnail: 'https://via.placeholder.com/500x300',
      description: 'Learn essential React fundamentals.',
    },
    {
      id: 2,
      title: 'Material UI Full Guide',
      thumbnail: 'https://via.placeholder.com/500x300',
      description: 'Understanding MUI components & styling.',
    },
  ];

  const handleCardClick = (video: any) => {
    setActiveVideo(video);
    setOpen(true);
  };

  return (
    <>
      {/* Cards List */}
      <Box p={2} display="grid" gap={4} gridTemplateColumns="repeat(auto-fit, minmax(240px, 1fr))">
        {videos.map((video) => (
          <VideoCardWrapper key={video.id} onClick={() => handleCardClick(video)}>
            <CardMedia component="img" height="160" image={video.thumbnail} />
            <CardContent>
              <Typography variant="subtitle1" noWrap>
                {video.title}
              </Typography>
            </CardContent>
          </VideoCardWrapper>
        ))}
      </Box>

      {/* Backdrop */}
      <Backdrop open={open} onClick={() => setOpen(false)} />

      {/* Popup */}
      <PopupCard open={open}>
        {activeVideo && (
          <>
            <CardMedia component="img" height="240" image={activeVideo.thumbnail} />
            <CardContent>
              <Typography variant="h6">{activeVideo.title}</Typography>
              <Typography variant="body2" color="text.secondary">
                {activeVideo.description}
              </Typography>
            </CardContent>
          </>
        )}
      </PopupCard>
    </>
  );
}
