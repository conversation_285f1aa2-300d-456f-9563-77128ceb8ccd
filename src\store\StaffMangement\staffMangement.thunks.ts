import api from '@/api';
import { CreateResponse, CreateResponseMulti, DeleteResponse, UpdateResponse } from '@/types/Common';
import {
  AddCTSAllocationMapInfo,
  CTSAllocationCreateUpdateInfo,
  CTSAllocationMappedInfo,
  CTSDetailsListPagedData,
  CTSDetailsListRequest,
  CTSFilterInfo,
  DeleteCTSAllocationRequest,
  StaffCreateRequest,
  StaffCreateRow,
  StaffListInfo,
  StaffListPagedData,
  StaffListRequest,
} from '@/types/StaffManagement';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchStaffDataList = createAsyncThunk<StaffListPagedData, StaffListRequest, { rejectValue: string }>(
  'StaffManagement/list',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.GetStaffDataList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Staff List');
    }
  }
);
export const fetchStaff = createAsyncThunk<any, any, { rejectValue: string }>(
  'StaffManagement/lists',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.GetStaff(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Staff List');
    }
  }
);

export const addNewStaff = createAsyncThunk<CreateResponse, StaffCreateRequest, { rejectValue: string }>(
  'StaffManagement/addnew',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.AddNewStaff(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding new Staff');
    }
  }
);

export const addNewMultipleStaff = createAsyncThunk<CreateResponseMulti, StaffCreateRow[], { rejectValue: string }>(
  'StaffManagement/addnewmultiple',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.AddMultipleStaff(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in adding multiple Staff');
    }
  }
);

export const updateStaff = createAsyncThunk<UpdateResponse, StaffListInfo, { rejectValue: string }>(
  'StaffManagement/update',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.UpdateStaff(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in updating Staff');
    }
  }
);

export const deleteStaff = createAsyncThunk<DeleteResponse, number, { rejectValue: string }>(
  'StaffManagement/delete',
  async (StaffId, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.DeleteStaff(StaffId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting Staff');
    }
  }
);

export const fetchCTSFilter = createAsyncThunk<CTSFilterInfo, number | undefined, { rejectValue: string }>(
  'StaffManagement/CTSFilterList',
  async (adminId, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.GetCTSFilter(adminId);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching CTS Filter List');
    }
  }
);

export const fetchCTSList = createAsyncThunk<CTSDetailsListPagedData, CTSDetailsListRequest, { rejectValue: string }>(
  'StaffManagement/CTSList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.GetCTSList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching CTS Details List');
    }
  }
);

export const createCTSAllocationSingle = createAsyncThunk<
  CreateResponse,
  CTSAllocationCreateUpdateInfo,
  { rejectValue: string }
>('StaffManagement/createCTSAllocationSingle', async (request, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.AddCTSAllocationSingle(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in adding CTS Allocation Single');
  }
});

export const fetchCTSUpdateDetail = createAsyncThunk<
  CTSAllocationCreateUpdateInfo,
  { adminId: number | undefined; cteacherId: number | undefined },
  { rejectValue: string }
>('StaffManagement/fetchCTSUpdateDetail', async ({ adminId, cteacherId }, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.GetCTSUpdateDetail(adminId, cteacherId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching CTS Update Detail');
  }
});

export const updateCTSAllocationSingle = createAsyncThunk<
  UpdateResponse,
  CTSAllocationCreateUpdateInfo,
  { rejectValue: string }
>('StaffManagement/updateCTSAllocationSingle', async (request, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.UpdateCTSAllocationSingle(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in updating Update CTS Allocation Single');
  }
});

export const fetchCTSMapping = createAsyncThunk<
  CTSAllocationMappedInfo[],
  { adminId: number | undefined; academicId: number | undefined; classId: number | undefined },
  { rejectValue: string }
>('StaffManagement/fetchCTSMapping', async ({ adminId, academicId, classId }, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.GetCTSMapping(adminId, academicId, classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching CTS Allocation Class Wise Mapping');
  }
});

export const AddCTSAllocationMap = createAsyncThunk<
  CreateResponseMulti,
  AddCTSAllocationMapInfo[],
  { rejectValue: string }
>('StaffManagement/addCTSAllocationMap', async (request, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.AddCTSAllocationMap(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in adding multiple CTS Allocation Map');
  }
});

export const fetchCTSTeacherWiseMapiing = createAsyncThunk<
  CTSAllocationMappedInfo[],
  { adminId: number | undefined; academicId: number | undefined; staffId: number | undefined },
  { rejectValue: string }
>('StaffManagement/fetchCTSTeacherWiseMapping', async ({ adminId, academicId, staffId }, { rejectWithValue }) => {
  try {
    const response = await api.StaffManagement.GetCTSTeacherWiseMapiing(adminId, academicId, staffId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching CTS Allocation Teacher Wise Mapping');
  }
});

export const deleteCTS = createAsyncThunk<DeleteResponse, DeleteCTSAllocationRequest[], { rejectValue: string }>(
  'manageFee/deleteCTS',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.StaffManagement.DeleteCTS(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in deleting CTS List');
    }
  }
);