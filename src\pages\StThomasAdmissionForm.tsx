/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
import { breakPointsMinwidth } from '@/config/breakpoints';
import styled from 'styled-components';
import Page from '@/components/shared/Page';
import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import successIcon from '@/assets/MessageIcons/success.json';
import uploadFiles from '@/assets/NotificationIcons/upload.gif';
import { v4 as uuidv4 } from 'uuid';

import {
  FormControlLabel,
  Grid,
  Stack,
  useTheme,
  TextField,
  Select,
  MenuItem,
  Button,
  Typography,
  Box,
  FormControl,
  Tabs,
  Tab,
  AppBar,
  Checkbox,
  Snackbar,
  Alert,
} from '@mui/material';
import DatePickers from '@/components/shared/Selections/DatePicker';
import Success from '@/assets/Registration/success.gif';
import StThomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import Fail from '@/assets/Registration/fail.gif';
import Loading from '@/assets/Registration/loading.gif';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { ErrorIcon } from '@/theme/overrides/CustomIcons';
import axios from 'axios';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import LoadingPopup from '@/components/shared/Popup/LoadingPopup';
import { LoadingMessage } from '@/components/shared/Popup/LoadingMessage';
import dayjs, { Dayjs } from 'dayjs';
import CheckIcon from '@mui/icons-material/Check';
import { SlUser, SlUserFemale } from 'react-icons/sl';
import { ArrowBack, ArrowForward, CheckCircle } from '@mui/icons-material';
import InputFileUpload from '@/components/shared/Selections/FilesUploadTextField';
import ImageCropper from '@/components/shared/ImageUploadWithCropper';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Link, useNavigate } from 'react-router-dom';
import { AdmissionFormTypes } from '@/features/AdmissinFormPrint';
import Popup from '@/components/shared/Popup/Popup';
import typography from '@/theme/card';

const StThoamsAdmissionFormRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[900]};
  /* min-height: calc(100vh - 160px); */
  /* .MuiOutlinedInput-root {
    border-radius: '10px'; // No border radius
  } */
  /* padding: 1rem; */

  .Card {
    min-height: calc(100vh - 35px);
  }
  /* @media ${breakPointsMinwidth.lg} {
    padding: 2rem;
  } */
`;

type ClassSectionArray = {
  sectionId: number;
  sectionName: string;
  DobRangeFrom: string;
  DobRangeTo: string;
  availableSeat: number;
};

export type FileObjTypes = {
  id: number;
  name: string;
  type: string;
  imageUrl?: string;
  originalFile: File;
  fieldName?: string;
};

interface UploadPropsTypes {
  uploadedFile: FileObjTypes[]; // Ensure this expects an array
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => {
  return value === index && <Box>{children}</Box>;
};

// --- NEW: options for Religion and Caste (pass text values to API) ---
const religionOptions = [
  'HINDU',
  'MUSLIM',
  'CHRISTIAN',
  'BUDDHIST',
  'SIKH',
  'PARSI',
  'JAIN',
  'JEW',
  'OTHER NON MINORITY',
];

const casteOptions = ['SC', 'ST', 'VJ(A)', 'NT(B)', 'NT(C)', 'NT(D)', 'OBC', 'SBC', 'OPEN', 'SEBC', 'EWS'];

function StThomasAdmissionForm() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { confirm } = useConfirm();
  //   const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = React.useState(false);
  const maxDate = dayjs(new Date());
  const [activeTab, setActiveTab] = useState(0);
  const [previousTab, setPreviousTab] = useState<number | null>(null);
  const [studentDOB, setStudentDOB] = React.useState<Dayjs | string>('');
  const [cellValidationError, setCellValidationError] = useState(false);
  const [classSections, setClassSections] = useState<ClassSectionArray[]>([]);
  // const [dobRangeFrom, setDobRangeFrom] = useState<string>('');
  // const [dobRangeTo, setDobRangeTo] = useState<string>('');
  const [uploaded, setUploaded] = useState<FileObjTypes[]>([]);
  const [uploadedFile, setUploadedFile] = useState<FileObjTypes | undefined | string>('');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [availableSeat, setAvailableSeat] = useState<number>(0);
  const [availableSeatError, setAvailableSeatError] = useState(false);
  const [dobAgeError, setDobAgeError] = useState<boolean>(false);
  const [dobSnackbarOpen, setDobSnackbarOpen] = useState(false);
  const [dobSnackbarMsg, setDobSnackbarMsg] = useState<string>('');
  const [dobRangeFrom, setDobRangeFrom] = useState<string>(dayjs('01-07-2022', 'DD-MM-YYYY').format('DD-MM-YYYY'));
  const [dobRangeTo, setDobRangeTo] = useState<string>(dayjs('31-12-2023', 'DD-MM-YYYY').format('DD-MM-YYYY'));
  const ageCriteriaLabel =
    dobRangeFrom && dobRangeTo
      ? `Age criteria: ${dayjs(dobRangeFrom, 'DD-MM-YYYY').format('DD-MM-YYYY')} to ${dayjs(
          dobRangeTo,
          'DD-MM-YYYY'
        ).format('DD-MM-YYYY')}`
      : '';
  // const ageCriteriaLabel =
  //   dobRangeFrom && dobRangeTo
  //     ? `Age criteria: ${dayjs(dobRangeFrom).format('DD-MM-YYYY')} to ${dayjs('2023-12-31').format('DD-MM-YYYY')}`
  //     : '';

  const tabPanelRef = useRef(null);

  const [submitting, setSubmitting] = useState(false);

  const showConfirmation = useCallback(
    async (successMessage: ReactElement, title: string) => {
      const confirmation = confirm(successMessage, title, {
        okLabel: 'Ok',
        showOnlyOk: true,
      });

      return confirmation;
    },
    [confirm]
  );
  // const onSave =

  useEffect(() => {
    console.log('dobRangeFrom::::----', dayjs(dobRangeFrom).format('MM/DD/YYYY'));
    console.log('SDob::::----', dayjs(studentDOB).format('DD/MM/YYYY'));
    const fetchData = async () => {
      try {
        const response = await axios.get(
          'https://stmregapi.pasdaily.in/ElixirApi/StudentSet/TakeSectionList?SchoolId=1'
        );

        // Extract SectionNamen and add a random ID to each object
        const sectionNames = response.data.map((item: any) => ({
          sectionId: item.SectionId, // Generate random ID
          sectionName: item.SectionName, // Keep SectionName
          DobRangeFrom: item.DobRangeFrom,
          DobRangeTo: item.DobRangeTo,
          availableSeat: item.AvailableSeat,
        }));

        setClassSections(sectionNames);
        console.log('Section Names:', sectionNames);
        console.log('response data Section Names:', response.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, [dobRangeFrom, studentDOB]);

  const CreateEditMessageTempValidationSchema = Yup.object({
    ClassName: Yup.string().required('Kindly select the Class.'),
    Surname: Yup.string().required('Kindly enter the Surname.'),
    StudentName: Yup.string().required('Kindly enter the Student Name.'),
    FathersName: Yup.string().required('Kindly enter the Father’s Name.'),
    SGender: Yup.string().oneOf(['0', '1'], 'Kindly select a valid Gender.').required('Kindly select the Gender.'),
    SDob: Yup.string().required('Kindly enter the Date of Birth.'),
    SdobInWords: Yup.string().required('Kindly enter the Date of Birth in words.'),
    // MobileNumber: Yup.string().required('Kindly enter the Mobile Number.'),
    SPlaceOfBorth: Yup.string().required('Kindly enter the Place of Birth.'),
    SMotherTongue: Yup.string().required('Kindly enter the Mother Tongue.'),
    SReligion: Yup.string().required('Kindly select the Religion.'),
    SCaste: Yup.string().required('Kindly select the Caste.'),
    SNationality: Yup.string().required('Kindly enter the Nationality.'),
    // SchoolLastAttended: Yup.string().required('Kindly enter the Last School Attended.'),
    SAddress1: Yup.string().required('Kindly enter Address Line 1.'),
    FCompanyYear: Yup.string().required('Kindly enter the Number of Years in Company.'),
    FOfficeMobile: Yup.string()
      .required('Kindly enter the Father’s Office Mobile Number.')
      .matches(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
    MotherName: Yup.string().required('Kindly enter the Mother’s Name.'),
    birthCertificate: Yup.mixed().required('Birth Certificate is required.'),
    proof: Yup.mixed().required('Leaving Document is required.'),
  });

  const {
    values: {
      ClassName,
      Surname,
      StudentName,
      FathersName,
      SGender,
      SDob,
      SdobInWords,
      SPlaceOfBorth,
      SMotherTongue,
      SReligion,
      SCaste,
      SNationality,
      SchoolLastAttended,
      SAddress1,
      FOccupation,
      FCompanyYear,
      FOfficeAddress,
      FOfficeMobile,
      FOfficeTelephone,
      MotherName,
      MCompanyYear,
      GuardianName,
      SchoolTransport,
      StopName,
      birthCertificate, // Store birth certificate image
      proof,
    },
    handleChange,
    setFieldValue,
    setFieldError,
    handleBlur,
    handleSubmit,
    touched,
    errors,
  } = useFormik<AdmissionFormTypes>({
    initialValues: {
      ClassName: '',
      Surname: '',
      StudentName: '',
      FathersName: '',
      SGender: -1,
      SDob: '',
      SdobInWords: '',
      SPlaceOfBorth: '',
      SMotherTongue: '',
      SReligion: '',
      SCaste: '',
      SNationality: '',
      SchoolLastAttended: '',
      SAddress1: '',
      FMotherTongue: '',
      FOccupation: '',
      FCompanyYear: '',
      FOfficeMobile: '',
      MotherName: '',
      MCompanyYear: '',
      GuardianName: '',
      SchoolTransport: -1,
      StopName: '',
      birthCertificate: '', // Store birth certificate image
      proof: '',
    },
    validationSchema: CreateEditMessageTempValidationSchema,
    onSubmit: async (values) => {
      console.error('values::::', values);
      try {
        // Construct the value object including the Dob field
        const value = {
          ...values,
          // SGender: selectedGender,
          // SchoolTransport: selectedTransport,
          SDob: studentDOB ? dayjs(studentDOB).format('DD/MM/YYYY') : '', // Format the date before assigning
          FileDetails: [
            {
              FileTitle: 'Student Birth Certificate',
              FileName: birthCertificate,
            },
            {
              FileTitle: 'Address Proof',
              FileName: proof,
            },
          ],
        };

        setSubmitting(true);
        console.log('values::::', value);
        const response = await axios.post(
          'https://stmregapi.pasdaily.in/ElixirApi/StudentSet/OnlineAdmissionTherese',
          value
        );
        console.log('response.data::::', response.data);
        const successMessages = response.data.RESULT === 'SUCCESS';
        const errorMessages = response.data.RESULT === 'FAILED';
        const existMessages = response.data.RESULT === 'EXIST';
        // const POPUPMESSAGE = ;
        setSubmitting(false);
        if (successMessages) {
          await showConfirmation(<SuccessMessage icon={Success} message={response.data.POPUPMESSAGE} />, '');
          navigate('/print-admission-form', { state: { details: response.data.DETAILS } });
        } else if (existMessages) {
          await showConfirmation(<ErrorMessage icon={Fail} message={response.data.POPUPMESSAGE} />, '');
        } else {
          await showConfirmation(<ErrorMessage icon={Fail} message={response.data.POPUPMESSAGE} />, '');
        }
      } catch (error) {
        setSubmitting(false);
        await showConfirmation(
          <ErrorMessage icon={Fail} message="Something went wrong with the registration. Please try again later." />,
          ''
        );
        console.error('Error submitting form:', error);
      } finally {
        setSubmitting(false);
      }
    },
    validateOnBlur: false,
    // validate: (messageVals) => {
    //   const errorObj: any = {};
    //   messageVals.messageContent.forEach(async (classRow, rowIndex, arr) => {
    //     if (arr.some((x, i) => classRow.Class !== '' && x.Class === classRow.Class && i !== rowIndex)) {
    //       if (!errorObj.classes) {
    //         errorObj.classes = [];
    //       }
    //       errorObj.classes[rowIndex] = {};
    //       errorObj.classes[rowIndex].Class = 'Duplicate class name';
    //     }
    //   });
    //   return errorObj;
    // },
  });

  // useEffect(() => {
  //   console.log('passportPhoto::::----', passportPhoto);
  // }, [passportPhoto]);

  const [loadingField, setLoadingField] = useState<string | null>(null);
  const [successIconShow, setSuccessIconShow] = useState<{ [key: string]: string }>({});
  // inside AdmissionForm() component, before JSX return (or right before rendering step 1)
  const earlyClasses = ['NURSERY', 'JR KG', 'SR KG'];
  const isEarlyYears = typeof ClassName === 'string' && earlyClasses.includes(ClassName);

  const onUpload = async (fieldName: string) => {
    console.log('fieldName', fieldName);
    setLoadingField(fieldName); // Set loading state for the clicked button

    try {
      console.log('uploaded', uploaded);

      const formData = new FormData();
      uploaded.forEach((file) => {
        if (file.fieldName === fieldName) {
          formData.append('files', file.originalFile);
        }
      });
      console.log('formData::::----', formData);

      const uploadResponse = await axios.post('http://stm.passdaily.in/ElixirApi/StudentSet/UploadFiles', formData);

      const uploadedFilesDetails = uploadResponse.data.DETAILS;
      setFieldValue(fieldName, uploadedFilesDetails); // Store the uploaded file details in formik state
      const uploadedFilesResult = uploadResponse.data.RESULT;
      console.log('uploadedFilesDetails', uploadedFilesDetails);

      // Replace upload button with success state
      if (uploadedFilesResult === 'SUCCESS') {
        console.log('successIconShow::::----', successIconShow);

        setSuccessIconShow((prevState) => ({
          ...prevState,
          [fieldName]: 'Success', // Mark this field as successfully uploaded
        }));
      }
      // **Remove uploaded file from state**
      // setUploaded((prevUploaded) => prevUploaded.filter((file) => file.fieldName !== fieldName));
    } catch (error) {
      console.error('Upload failed', error);
    } finally {
      setLoadingField(null); // Reset loading state after upload completes
    }
  };

  const handleGenderSelect = (gender: number) => {
    setFieldValue('SGender', gender); // Store as string to match Yup validation
    handleBlur({ target: { name: 'SGender' } }); // Trigger validation
  };

  const handleTransportSelect = (transport: number) => {
    setFieldValue('SchoolTransport', transport); // Store as string to match Yup validation
    handleBlur({ target: { name: 'SchoolTransport' } }); // Trigger validation
  };

  // Adjust step1 required check for early years (skip SchoolLastAttended)
  // const isAnyRequiredFieldEmptyStep1 = [
  //   Surname,
  //   Syllabus,
  //   ClassName,
  //   StudentName,
  //   FathersName,
  //   SGender,
  //   studentDOB,
  //   SdobInWords,
  //   SPlaceOfBorth,
  //   SMotherTongue,
  //   SReligion,
  //   SCaste,
  //   SNationality,
  //   // conditionally include SchoolLastAttended
  //   ...(isEarlyYears ? [] : [SchoolLastAttended]),
  //   SAddress1,
  //   SAddress2,
  //   SAddress3,
  //   StudentBloodGroup,
  //   SAadharNo,
  // ].some((field) => field === '' || field === 'Select' || field === -1);

  // form ready for final submit: all steps required fields present, not submitting, no DOB/seat errors

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    const fileInput = event.target;
    const file = fileInput.files?.[0];
    if (!file) return;

    // setFieldValue(fieldName, file); // Store file in Formik state

    const randomId = Math.floor(Math.random() * 1_000_000);
    const fileObj: any = {
      id: randomId,
      name: file.name,
      type: file.type,
      originalFile: file,
      fieldName,
    };

    if (file.type.startsWith('image/')) {
      const imgUrl = URL.createObjectURL(file);
      fileObj.imageUrl = imgUrl;

      setUploadedFile(fileObj);
      // setUploaded(() => [fileObj]);
      if (uploadFiles) {
        setIsDialogOpen(true);
      }

      console.log('Uploaded Image:', fileObj);

      // Cleanup object URL to prevent memory leaks
      setTimeout(() => URL.revokeObjectURL(imgUrl), 5000);
    } else if (file.type.startsWith('application/pdf')) {
      // setUploaded(fileObj);
      // setUploaded((prevUploaded) => [fileObj]);
      // setUploaded((prevUploaded) => [...prevUploaded, fileObj]);
      setUploaded((prevUploaded) => [
        ...prevUploaded.filter((f) => f.fieldName !== fieldName), // Remove existing object with same fieldName
        fileObj, // Add new object
      ]);
      console.log('Uploaded PDF:', fileObj);
    }
    // Reset the input field to allow selecting the same file again
    fileInput.value = '';
  };

  // Function to remove a file
  const handleRemoveFile = (fieldName: string) => {
    setUploaded((prevUploaded) => {
      const updatedFiles = prevUploaded.filter((file) => file.fieldName !== fieldName);
      return [...updatedFiles]; // Ensure state updates properly
    });

    setFieldValue(fieldName, ''); // Clear Formik field value
    setSuccessIconShow((prevState) => ({
      ...prevState,
      [fieldName]: '', // Mark this field as successfully uploaded
    }));

    // Reset the file input value
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    console.log('Uploaded::::----', uploaded);
  };

  const [showPage, setShowPage] = useState<boolean>(false);

  return showPage ? (
    <Stack height="100%" width="100%" direction="row" alignItems="center" justifyContent="center">
      <Typography variant="h6" textAlign="center" p={2}>
        The application portal will open on March 4th, 2025
      </Typography>
    </Stack>
  ) : (
    <Page title="AdmissionForm">
      <StThoamsAdmissionFormRoot>
        <Stack direction="row" justifyContent="end" p={2}>
          <Button size="small" color="success" variant="outlined" component={Link} to="/stm-print-admission-form">
            Print Form
          </Button>
        </Stack>

        <Stack alignItems="center" className="container-fluid">
          <img width={100} src={StThomasLogo} alt="StThomasLogo" />
          {/* <Typography
            textAlign="center"
            fontSize={10}
            bgcolor={theme.palette.error.main}
            color={theme.palette.common.white}
            px={1}
            variant="h5"
            fontWeight={600}
          >
            ST. THERESE CONVENT SCHOOL
          </Typography>
          <Typography
            textAlign="center"
            fontSize={10}
            color={theme.palette.error.main}
            fontWeight={600}
            mt={1}
            variant="h5"
          >
            DOMBIVLI
          </Typography> */}
          <Typography
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h4"
            fontSize={{ xs: '20px', sm: '28px', md: '2.125rem' }}
          >
            ST. THOMAS SCHOOL
          </Typography>
          <Typography
            fontSize={{ xs: '13px', sm: '16px', md: '18px' }}
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h6"
          >
            Behind Ebenzer Church, Near Shankeshwar Nagar <br /> Nandivil-Bhoper Road, Dombivli(E) - 421 201
          </Typography>
          <Typography
            fontSize={{ xs: '13px', sm: '16px', md: '18px' }}
            textAlign="center"
            fontWeight={600}
            mt={1}
            variant="h6"
          >
            {/* <EMAIL>, 8928851277 */}
          </Typography>
        </Stack>
        <Typography
          className="container-fluid"
          bgcolor={theme.palette.grey[300]}
          textAlign="center"
          fontWeight={600}
          mt={5}
          variant="h5"
          py={3}
          fontSize={{ xs: '16px', sm: '18px', md: '1.5rem' }}
        >
          <u> APPLICATION FOR ADMISSION 2026-2027</u>
        </Typography>
        <Stack px={{ xs: 0, md: 10 }}>
          <Stack
            component="form"
            autoComplete="off"
            noValidate
            onSubmit={handleSubmit}
            ref={tabPanelRef}
            className="container-fluid"
          >
            <TabPanel value={activeTab} index={0}>
              <Box sx={{ pt: 10 }}>
                <Grid container columnSpacing={{ xs: 10, md: 10, lg: 20, xl: 25, xxl: 50 }} rowSpacing={2}>
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Standard in which admission is sought *
                      </Typography>
                      <Select
                        size="small"
                        disabled={submitting}
                        variant="outlined"
                        name="ClassName"
                        onBlur={handleBlur}
                        required
                        value={ClassName || 'Select'}
                        onChange={(e) => {
                          handleChange(e); // Ensure Formik state updates properly

                          const selectedClass = classSections.find((item) => item.sectionName === e.target.value); // Find the matching class
                          if (selectedClass) {
                            setDobRangeFrom(dayjs(selectedClass.DobRangeFrom).format('MM/DD/YYYY')); // Set dobRangeFrom directly
                            setDobRangeTo(dayjs(selectedClass.DobRangeTo).format('MM/DD/YYYY')); // Set dobRangeFrom directly
                            // Check if availableSeat is 0
                            if (selectedClass.availableSeat === 0 || selectedClass.availableSeat < 0) {
                              setAvailableSeatError(true);
                              showConfirmation(
                                <ErrorMessage
                                  icon={Fail}
                                  message={
                                    <Typography color="red" fontSize="25px" variant="subtitle2">
                                      Admission closed
                                    </Typography>
                                  }
                                />,
                                ''
                              );
                              // setAvailableSeat(0)
                            } else {
                              setAvailableSeatError(false);
                            }
                          } else {
                            setDobRangeFrom(''); // Reset if no match
                            setDobRangeTo(''); // Reset if no match
                          }
                        }}
                        error={touched.ClassName && !!errors.ClassName}
                      >
                        <MenuItem value="Select">Select</MenuItem>
                        {classSections.map((item) => (
                          <MenuItem key={item.sectionId} value={item.sectionName}>
                            {item.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                      {/* Error message for availableSeat */}
                      {availableSeatError && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          Admission closed
                        </Typography>
                      )}
                      {touched.ClassName && !!errors.ClassName && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          {errors.ClassName}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Surname *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="Surname"
                        value={Surname}
                        onChange={handleChange}
                        error={touched.Surname && !!errors.Surname}
                        helperText={touched.Surname && typeof errors.Surname === 'string' ? errors.Surname : undefined}
                        InputProps={{
                          endAdornment: touched.Surname && !!errors.Surname && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Student's Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="StudentName"
                        value={StudentName}
                        onChange={handleChange}
                        error={touched.StudentName && !!errors.StudentName}
                        helperText={
                          touched.StudentName && typeof errors.StudentName === 'string' ? errors.StudentName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.StudentName && !!errors.StudentName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Father&apos;s Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FathersName"
                        value={FathersName}
                        onChange={handleChange}
                        error={touched.FathersName && !!errors.FathersName}
                        helperText={
                          touched.FathersName && typeof errors.FathersName === 'string' ? errors.FathersName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FathersName && !!errors.FathersName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mother Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="MotherName"
                        value={MotherName}
                        onChange={handleChange}
                        error={touched.MotherName && !!errors.MotherName}
                        helperText={
                          touched.MotherName && typeof errors.MotherName === 'string' ? errors.MotherName : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MotherName && !!errors.MotherName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Select Gender *
                      </Typography>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          gap: { xs: 10, sm: 20, md: 30, lg: 10, xl: 20, xxl: 30 },
                        }}
                      >
                        <Button
                          disabled={submitting || availableSeatError}
                          fullWidth
                          variant={SGender === 0 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleGenderSelect(0)}
                          startIcon={<SlUser />}
                        >
                          Male
                        </Button>
                        <Button
                          disabled={submitting || availableSeatError}
                          fullWidth
                          variant={SGender === 1 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleGenderSelect(1)}
                          startIcon={<SlUserFemale />}
                        >
                          Female
                        </Button>
                      </Box>
                      {touched.SGender && errors.SGender && typeof errors.SGender === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.SGender}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xxl={6} xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Stack direction="row" alignItems="center" gap={2}>
                        <Typography variant="h6" fontSize={13}>
                          Date of Birth (In figure) *
                        </Typography>
                        {/* {ageCriteriaLabel && (
                          <Typography
                            variant="caption"
                            sx={{ mb: 1, display: 'block', color: dobAgeError ? 'error.main' : 'text.secondary' }}
                          >
                            Age criteria: 01-07-2022 to 31-12-2023
                          </Typography>
                        )} */}
                      </Stack>

                      <DatePickers
                        disabled={submitting || availableSeatError}
                        // fullWidth={{
                        //   '@media (min-width: 1300px)': { width: '35.3vw' },
                        //   '@media (min-width: 1300px)': { width: '35.3vw' },
                        // }}
                        fullWidth={{ xs: '100%', xxl: '34vw', xl: '31vw' }}
                        // maxWidth="800px"
                        // width="300px"
                        name="SDob"
                        value={dayjs(SDob, 'DD-MM-YYYY')} // Format the date before passing it
                        onChange={(e) => {
                          setFieldValue('SDob', dayjs(e).format('DD/MM/YYYY')); // Store the selected date in the state
                          const selectedDate = e ? dayjs(e) : ''; // Convert the selected date to dayjs object
                          setStudentDOB(selectedDate); // Store the selected date in the state
                          // validate against dobRangeFrom / dobRangeTo if available
                          if (dobRangeFrom && dobRangeTo && selectedDate) {
                            const from = dayjs(dobRangeFrom);
                            const to = dayjs(dobRangeTo);
                            const ok = !selectedDate.isBefore(from, 'day') && !selectedDate.isAfter(to, 'day');
                            if (!ok) {
                              setDobAgeError(true);
                              setFieldError('SDob', 'Date of birth does not meet age criteria for selected class.');
                            } else {
                              setDobAgeError(false);
                              setFieldError('SDob', '');
                            }
                          } else {
                            // no range to validate against - clear any prior error
                            setDobAgeError(false);
                            setFieldError('SDob', '');
                          }
                          const selectedDT = e ? dayjs(e) : null;
                          // if no selected date just clear
                          if (!selectedDT) {
                            setFieldValue('SDob', '');
                            setStudentDOB('');
                            setDobAgeError(false);
                            setFieldError('SDob', '');
                            return;
                          }

                          // Validate against dobRangeFrom / dobRangeTo if available
                          if (dobRangeFrom && dobRangeTo) {
                            const from = dayjs(dobRangeFrom, ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD']);
                            // const to = dayjs(dobRangeTo, ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD']);
                            const to = '2023-12-31';
                            const ok = !selectedDT.isBefore(from, 'day') && !selectedDT.isAfter(to, 'day');
                            if (!ok) {
                              // Show snackbar  set form error and clear the invalid value so user must re-enter
                              setDobAgeError(true);
                              setFieldError('SDob', 'Date of birth does not meet age criteria for selected class.');
                              setDobSnackbarMsg(
                                // `DOB must be between ${from.format('DD-MM-YYYY')} and ${to.format('DD-MM-YYYY')}`
                                `DOB must be between 01-07-2022 and 31-12-2023`
                              );
                              setDobSnackbarOpen(true);
                              // clear the invalid entry
                              setFieldValue('SDob', '');
                              setStudentDOB('');
                              return;
                            }
                          }

                          // valid -> set value and clear any age error
                          setFieldValue('SDob', selectedDT.format('DD/MM/YYYY'));
                          setStudentDOB(selectedDT);
                          setDobAgeError(false);
                          setFieldError('SDob', '');
                        }}
                        // maxDate={maxDate || dayjs(dobRangeTo)}
                        maxDate={dayjs(dobRangeTo)}
                        minDate={dayjs(dobRangeFrom)}
                        // minDate={dayjs('01-07-2022', 'DD-MM-YYYY')} // ✅ correct min date
                        // maxDate={dayjs('31-12-2023', 'DD-MM-YYYY')} // ✅ correct max date
                        variant={{ textField: { variant: 'outlined', fullWidth: true } }}
                      />
                      {touched.SDob && errors.SDob && typeof errors.SDob === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.SDob}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Date of Birth (In words) *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SdobInWords"
                        value={SdobInWords}
                        onChange={handleChange}
                        error={touched.SdobInWords && !!errors.SdobInWords}
                        helperText={
                          touched.SdobInWords && typeof errors.SdobInWords === 'string' ? errors.SdobInWords : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SdobInWords && !!errors.SdobInWords && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Place of Birth *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SPlaceOfBorth"
                        value={SPlaceOfBorth}
                        onChange={handleChange}
                        error={touched.SPlaceOfBorth && !!errors.SPlaceOfBorth}
                        helperText={
                          touched.SPlaceOfBorth && typeof errors.SPlaceOfBorth === 'string'
                            ? errors.SPlaceOfBorth
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SPlaceOfBorth && !!errors.SPlaceOfBorth && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Grid container columnSpacing={{ xs: 10, sm: 20, md: 30, lg: 10, xl: 10, xxl: 15 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={6} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Mother Tongue *
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="SMotherTongue"
                            value={SMotherTongue}
                            onChange={handleChange}
                            error={touched.SMotherTongue && !!errors.SMotherTongue}
                            helperText={
                              touched.SMotherTongue && typeof errors.SMotherTongue === 'string'
                                ? errors.SMotherTongue
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.SMotherTongue && !!errors.SMotherTongue && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>

                      <Grid item xl={6} lg={6} md={12} xs={12}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Nationality *
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="SNationality"
                            value={SNationality}
                            onChange={handleChange}
                            error={touched.SNationality && !!errors.SNationality}
                            helperText={
                              touched.SNationality && typeof errors.SNationality === 'string'
                                ? errors.SNationality
                                : undefined
                            }
                            InputProps={{
                              endAdornment: touched.SNationality && !!errors.SNationality && (
                                <ErrorIcon color="error" />
                              ),
                            }}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    {/* <Grid container columnSpacing={{ xs: 10, sm: 20, md: 30, lg: 10, xl: 20, xxl: 30 }} rowSpacing={2}>
                      <Grid item xl={6} lg={6} md={6} xs={12}> */}
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Religion *
                      </Typography>
                      <Select
                        size="small"
                        name="SReligion"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        value={SReligion || ''}
                        onChange={handleChange}
                        displayEmpty
                        error={touched.SReligion && !!errors.SReligion}
                      >
                        <MenuItem value="">Select</MenuItem>
                        {religionOptions.map((opt) => (
                          <MenuItem key={opt} value={opt}>
                            {opt}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.SReligion && !!errors.SReligion && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          {errors.SReligion}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Guardian Name *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="GuardianName"
                        value={GuardianName}
                        onChange={handleChange}
                        error={touched.GuardianName && !!errors.GuardianName}
                        helperText={
                          touched.GuardianName && typeof errors.GuardianName === 'string'
                            ? errors.GuardianName
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.GuardianName && !!errors.GuardianName && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  {/* </Grid> */}
                  {/* </Grid> */}

                  <Grid item xl={6} lg={6} md={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Caste *
                      </Typography>
                      <Select
                        size="small"
                        name="SCaste"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        value={SCaste || ''}
                        onChange={handleChange}
                        displayEmpty
                        error={touched.SCaste && !!errors.SCaste}
                      >
                        <MenuItem value="">Select</MenuItem>
                        {casteOptions.map((opt) => (
                          <MenuItem key={opt} value={opt}>
                            {opt}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.SCaste && !!errors.SCaste && (
                        <Typography color="red" fontSize="12px" variant="subtitle1">
                          {errors.SCaste}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Full Residential Address *
                      </Typography>
                      <TextField
                        multiline
                        name="SAddress1"
                        value={SAddress1}
                        placeholder="Enter building name, flat number"
                        onChange={handleChange}
                        error={touched.SAddress1 && !!errors.SAddress1}
                        helperText={
                          touched.SAddress1 && typeof errors.SAddress1 === 'string' ? errors.SAddress1 : undefined
                        }
                        disabled={submitting || availableSeatError}
                        fullWidth
                        InputProps={{
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '1.4375em', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Relationship of Guardian to the Pupil
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="SchoolLastAttended"
                        value={SchoolLastAttended}
                        onChange={handleChange}
                        error={touched.SchoolLastAttended && !!errors.SchoolLastAttended}
                        helperText={
                          touched.SchoolLastAttended && typeof errors.SchoolLastAttended === 'string'
                            ? errors.SchoolLastAttended
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.SchoolLastAttended && !!errors.SchoolLastAttended && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Occupation of Father/Guardian
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FOccupation"
                        value={FOccupation}
                        onChange={handleChange}
                        error={touched.FOccupation && !!errors.FOccupation}
                        helperText={
                          touched.FOccupation && typeof errors.FOccupation === 'string' ? errors.FOccupation : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOccupation && !!errors.FOccupation && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Office Address
                      </Typography>
                      <TextField
                        multiline
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        variant="outlined"
                        name="FOfficeAddress"
                        value={FOfficeAddress}
                        onChange={handleChange}
                        error={touched.FOfficeAddress && !!errors.FOfficeAddress}
                        helperText={
                          touched.FOfficeAddress && typeof errors.FOfficeAddress === 'string'
                            ? errors.FOfficeAddress
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeAddress && !!errors.FOfficeAddress && (
                            <ErrorIcon color="error" />
                          ),
                          inputProps: {
                            style: { resize: 'vertical', width: '100%', minHeight: '1.4375em', maxHeight: '100px' },
                          },
                        }}
                      />
                    </FormControl>
                  </Grid>
                  {/* 
                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Office Mobile Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FOfficeMobile"
                        value={FOfficeMobile}
                        onChange={handleChange}
                        error={touched.FOfficeMobile && !!errors.FOfficeMobile}
                        helperText={
                          touched.FOfficeMobile && typeof errors.FOfficeMobile === 'string'
                            ? errors.FOfficeMobile
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeMobile && !!errors.FOfficeMobile && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid> */}

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Annual Income(Father)
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FCompanyYear"
                        value={FCompanyYear}
                        onChange={handleChange}
                        error={touched.FCompanyYear && !!errors.FCompanyYear}
                        helperText={
                          touched.FCompanyYear && typeof errors.FCompanyYear === 'string'
                            ? errors.FCompanyYear
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FCompanyYear && !!errors.FCompanyYear && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Annual Income(Mother)
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="MCompanyYear"
                        value={MCompanyYear}
                        onChange={handleChange}
                        error={touched.MCompanyYear && !!errors.MCompanyYear}
                        helperText={
                          touched.MCompanyYear && typeof errors.MCompanyYear === 'string'
                            ? errors.MCompanyYear
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.MCompanyYear && !!errors.MCompanyYear && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Telephone Number
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FOfficeTelephone"
                        value={FOfficeTelephone}
                        onChange={handleChange}
                        error={touched.FOfficeTelephone && !!errors.FOfficeTelephone}
                        helperText={
                          touched.FOfficeTelephone && typeof errors.FOfficeTelephone === 'string'
                            ? errors.FOfficeTelephone
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeTelephone && !!errors.FOfficeTelephone && (
                            <ErrorIcon color="error" />
                          ),
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Mobile Number *
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        onBlur={handleBlur}
                        disabled={submitting || availableSeatError}
                        color="primary"
                        required
                        type="number"
                        variant="outlined"
                        name="FOfficeMobile"
                        value={FOfficeMobile}
                        onChange={handleChange}
                        error={touched.FOfficeMobile && !!errors.FOfficeMobile}
                        helperText={
                          touched.FOfficeMobile && typeof errors.FOfficeMobile === 'string'
                            ? errors.FOfficeMobile
                            : undefined
                        }
                        InputProps={{
                          endAdornment: touched.FOfficeMobile && !!errors.FOfficeMobile && <ErrorIcon color="error" />,
                        }}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={
                        successIconShow.birthCertificate === 'Success'
                          ? 'end'
                          : birthCertificate === ''
                          ? 'end'
                          : 'center'
                      }
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Birth Certificate *
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded)
                                    ? uploaded.filter((f) => f.fieldName === 'birthCertificate')
                                    : ([] as FileObjTypes[])
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.birthCertificate === 'Success' || submitting || availableSeatError
                                }
                                fieldName="birthCertificate"
                                handleRemoveFile={() => handleRemoveFile('birthCertificate')}
                                onChange={(event) => handleImageUpload(event, 'birthCertificate')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'birthCertificate') &&
                          (successIconShow.birthCertificate === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'birthCertificate'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('birthCertificate')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'birthCertificate'} // Disable while uploading
                            >
                              {loadingField === 'birthCertificate' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.birthCertificate &&
                      errors.birthCertificate &&
                      typeof errors.birthCertificate === 'string' && (
                        <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                          {errors.birthCertificate}
                        </Typography>
                      )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <Stack
                      direction="row"
                      alignItems={successIconShow.proof === 'Success' ? 'end' : proof === '' ? 'end' : 'center'}
                      gap={2}
                    >
                      <FormControl fullWidth>
                        <ImageCropper
                          uploadedFile={uploadedFile}
                          setUploaded={setUploaded}
                          uploaded={uploaded}
                          setIsDialogOpen={setIsDialogOpen}
                          isDialogOpen={isDialogOpen}
                          // onImageUpload={handleImageUpload}
                          uploadButton={
                            <FormControl fullWidth>
                              <Typography variant="h6" fontSize={13}>
                                Leaving Certificate *
                              </Typography>
                              <InputFileUpload
                                name="upload"
                                uploadedFile={
                                  Array.isArray(uploaded)
                                    ? uploaded.filter((f) => f.fieldName === 'proof')
                                    : ([] as FileObjTypes[])
                                }
                                buttonLabel="Choose"
                                accept="image/*,application/pdf"
                                disabledTextfeild="disabled"
                                disabledUploadButton={
                                  successIconShow.proof === 'Success' || submitting || availableSeatError
                                }
                                fieldName="proof"
                                handleRemoveFile={() => handleRemoveFile('proof')}
                                onChange={(event) => handleImageUpload(event, 'proof')}
                                inputRef={fileInputRef} // Pass the ref to the file input
                              />
                            </FormControl>
                          }
                        />
                      </FormControl>
                      {Array.isArray(uploaded)
                        ? uploaded.find((f) => f.fieldName === 'proof') &&
                          (successIconShow.proof === 'Success' ? (
                            <Stack width={120} alignItems="center">
                              <Lottie animationData={successIcon} loop={false} style={{ width: '27px' }} />
                              <Typography variant="subtitle2" fontSize={8} color={theme.palette.success.main}>
                                Uploaded
                              </Typography>
                            </Stack>
                          ) : (
                            <LoadingButton
                              //  loadingPosition='start'
                              loading={loadingField === 'proof'}
                              startIcon={<CloudUploadIcon />}
                              onClick={() => onUpload('proof')}
                              variant="outlined"
                              color="success"
                              sx={{ width: 112, fontSize: 10, height: 38 }}
                              disabled={loadingField === 'proof'} // Disable while uploading
                            >
                              {loadingField === 'proof' ? 'Uploading...' : 'Upload'}
                            </LoadingButton>
                          ))
                        : []}
                    </Stack>
                    {touched.proof && errors.proof && typeof errors.proof === 'string' && (
                      <Typography color="red" fontSize="12px" mt={1} ml={2.5} variant="subtitle1">
                        {errors.proof}
                      </Typography>
                    )}
                  </Grid>

                  <Grid item xl={6} lg={6} md={12} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="h6" fontSize={13}>
                        Whether your child avail school&apos;s transport
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'start', gap: 2 }}>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={SchoolTransport === 1 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleTransportSelect(1)}
                          startIcon={SchoolTransport === 1 ? <CheckIcon /> : null}
                        >
                          Yes
                        </Button>
                        <Button
                          disabled={submitting || availableSeatError}
                          sx={{ width: 100 }}
                          variant={SchoolTransport === 0 ? 'contained' : 'outlined'}
                          color="secondary"
                          onClick={() => handleTransportSelect(0)}
                          startIcon={SchoolTransport === 0 ? <CheckIcon /> : null} // Conditionally show CheckIcon
                        >
                          No
                        </Button>
                      </Box>
                    </FormControl>
                    {SchoolTransport === 1 && (
                      <Grid item xl={12} lg={12} md={12} xs={12} mt={2}>
                        <FormControl fullWidth>
                          <Typography variant="h6" fontSize={13}>
                            Enter Bus Stop Pick Up Point
                          </Typography>
                          <TextField
                            fullWidth
                            size="small"
                            onBlur={handleBlur}
                            disabled={submitting || availableSeatError}
                            color="primary"
                            required
                            variant="outlined"
                            name="StopName"
                            value={StopName}
                            onChange={handleChange}
                            error={touched.StopName && !!errors.StopName}
                            helperText={
                              touched.StopName && typeof errors.StopName === 'string' ? errors.StopName : undefined
                            }
                            InputProps={{
                              endAdornment: touched.StopName && !!errors.StopName && <ErrorIcon color="error" />,
                            }}
                          />
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>

            <Box sx={{ display: 'flex', gap: 2, my: 5, justifyContent: 'center', textAlign: 'center' }}>
              <Button
                variant="outlined"
                color="info"
                sx={{ my: 3, width: 100 }}
                type="submit"
                endIcon={<CheckCircle />} // ➡️ Next Arrow Icon
              >
                Submit
              </Button>
            </Box>
          </Stack>
        </Stack>
      </StThoamsAdmissionFormRoot>
      {submitting && (
        <LoadingPopup
          // title="Message Creating"
          popupContent={<LoadingMessage icon={Loading} message="Enquiry Processing Please Wait..." />}
        />
      )}
      {/* <Popup
        size="xs"
        state={availableSeatError}
        // onClose={() => setVideoPopup(false)}
        popupContent={
          <Stack px={3} alignItems="center">
            <Typography color="red" fontSize="16px" mt={1} variant="subtitle1">
             Admission closed
            </Typography>
            <Button
              variant="outlined"
              color="secondary"
              sx={{ my: 3, width: 100 }}
              onClick={() => {
                setAvailableSeatError(false);
              }} // Decrement tab
            >
              Ok
            </Button>
          </Stack>
        }
      /> */}
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={cellValidationError}
        autoHideDuration={3000}
        onClose={() => setCellValidationError(false)}
        // message="Receipt number already exist"
      >
        <Alert severity="error" variant="filled">
          <Typography variant="subtitle2" display="flex" alignItems="center">
            Fill the required field
          </Typography>
        </Alert>
      </Snackbar>
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={dobSnackbarOpen}
        autoHideDuration={4000}
        onClose={() => setDobSnackbarOpen(false)}
      >
        <Alert onClose={() => setDobSnackbarOpen(false)} severity="warning" variant="standard">
          {dobSnackbarMsg}
        </Alert>
      </Snackbar>
    </Page>
  );
}

export default StThomasAdmissionForm;
