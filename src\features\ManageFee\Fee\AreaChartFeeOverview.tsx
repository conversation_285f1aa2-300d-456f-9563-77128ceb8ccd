import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { MenuItem, Select, SelectChangeEvent, Stack, Typography, useTheme, Box, colors } from '@mui/material';
import styled from 'styled-components';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewChartListData } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { ClassListInfo } from '@/types/AcademicManagement';
import NodataGraphIcon from '@/assets/Graph.png';
import { OverViewProps } from '@/types/Common';
import { fetchFeeOverviewChart } from '@/store/ManageFee/manageFee.thunks';

const StatisticRoot = styled.div`
  width: 100%;
  .apexcharts-menu-icon {
    display: none;
  }
`;

const AreaChartComponent = ({ academicId, feeTypeId }: OverViewProps) => {
  const { user } = useAuth();
  const theme = useTheme();
  const dispatch = useAppDispatch();

  const adminId: number | undefined = user?.accountId;

  const feeOverviewChartListData = useAppSelector(getFeeOverviewChartListData);

  const ClassData = useAppSelector(getClassData);

  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState<ClassListInfo>(AllClassOption);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) setClassOptions(selectedClass);
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewChart({ adminId, academicId, classId, feeTypeId }));
  }, [dispatch, adminId, classId, academicId, feeTypeId]);

  const totalFeeSum = feeOverviewChartListData.reduce(
    (acc, curr) => acc + parseFloat(curr.totalFeeCollected || '0'),
    0
  );

  const maxCollectedFee = Math.max(
    ...(feeOverviewChartListData.map((item) => parseFloat(item.totalFeeCollected)) || [0])
  );
  // const isBelowThreshold = maxCollectedFee < 600000;

  // const chartSeries = [
  //   {
  //     name: 'Current Year',
  //     data: feeOverviewChartListData.map((item) => parseFloat(item.totalFeeCollected)),
  //   },
  // ];

  const reorderAprToMar = (data: any[]) => [
    ...data.slice(3), // Apr–Dec
    ...data.slice(0, 3), // Jan–Mar
  ];

  const chartSeries = [
    {
      name: 'Current Year',
      data: reorderAprToMar(feeOverviewChartListData).map((item) => parseFloat(item.totalFeeCollected || '0')),
    },
    // {
    //   name: 'Previous Year (2024 - 2025)',
    //   data: reorderAprToMar(feeOverviewChartListData).map((item) => parseFloat(item.totalFeeCollected || '0')),
    // },
  ];

  const chartOptions = {
    chart: { type: 'area', toolbar: { show: false }, zoom: { enabled: false } },
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    grid: { borderColor: 'rgba(163, 174, 208, 0.3)', strokeDashArray: 3 },
    xaxis: {
      categories: [
        ...feeOverviewChartListData.map((item) => item.monthName).slice(3), // from April (index 3 if data starts Jan)
        ...feeOverviewChartListData.map((item) => item.monthName).slice(0, 3), // add Jan–Mar to the end
      ].map((name) => name.substring(0, 3)), // show Apr, May, etc.
      // categories: feeOverviewChartListData.map((item) => item.monthName.substring(0, 3)),
      labels: { style: { fontWeight: 600, fontSize: '11px', colors: theme.palette.chart.violet[0] } },
      axisBorder: { show: true },
      axisTicks: { show: false },
    },
    yaxis: { labels: { style: { fontWeight: 600, colors: theme.palette.chart.violet[0] } } },
    tooltip: {
      theme: 'dark',
      y: { formatter: (val: number) => `₹${val.toLocaleString()}` },
    },
    // colors: [isBelowThreshold ? theme.palette.chart.violet[0] : theme.palette.primary.main],
    colors: [theme.palette.chart.violet[0], theme.palette.primary.main],
    fill: {
      type: 'gradient',
      gradient: { shadeIntensity: 1, opacityFrom: 0.6, opacityTo: 0.1, stops: [0, 90, 100] },
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      labels: { colors: theme.palette.text.secondary },
    },
  };

  return (
    <StatisticRoot>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
        <Typography variant="subtitle2" fontSize={17} sx={{ fontFamily: 'Poppins Semibold' }}>
          Fee Statistics
        </Typography>

        <Select
          sx={{
            height: 30,
            // backgroundColor: theme.palette.grey[100],
            color: theme.palette.text.primary,
            fontSize: 13,
            // minWidth: 130,
          }}
          value={className}
          onChange={handleChange}
          displayEmpty
          MenuProps={{ PaperProps: { style: { maxHeight: '250px' } } }}
        >
          {classDataWithAllClass.map((item) => (
            <MenuItem key={item.classId} value={item.className}>
              {item.className}
            </MenuItem>
          ))}
        </Select>
      </Stack>

      <Box sx={{ overflowX: 'scroll', height: '300px' }}>
        {totalFeeSum === 0 ? (
          <Stack justifyContent="center" alignItems="center" spacing={1} mt={3}>
            <img width={140} src={NodataGraphIcon} alt="No Data" style={{ opacity: 0.7 }} />
            <Typography variant="subtitle2" color="textSecondary">
              No Fees Data in this Class.
            </Typography>
          </Stack>
        ) : (
          <Box sx={{ width: { sm: '100%', xs: '550px' }, height: { xs: '170px', sm: '180px' } }}>
            <Chart options={chartOptions} series={chartSeries} type="area" height="100%" />
          </Box>
        )}
      </Box>
    </StatisticRoot>
  );
};

export default AreaChartComponent;
