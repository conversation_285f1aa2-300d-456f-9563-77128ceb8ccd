import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import {
  ChapterFilterData,
  VideoClassListData,
  VideoClassListRequest,
} from '@/types/OnlineVideoClass';

/**
 * Fetch chapters list for filter dropdown
 * @param adminId - Admin identifier
 * @param academicId - Academic year identifier
 * @param classId - Class identifier
 * @param subjectId - Subject identifier (use -1 for all subjects)
 * @returns Promise with array of chapter filter data
 */
async function GetChapterFilterData(
  adminId: number,
  academicId: number,
  classId: number,
  subjectId: number
): Promise<APIResponse<ChapterFilterData[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined || subjectId === undefined) {
    throw new Error('adminId, academicId, classId, and subjectId are required');
  }

  const url = ApiUrls.GetChapterFilterData.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{subjectId}', subjectId.toString());

  const response = await privateApi.get<ChapterFilterData[]>(url);
  return response;
}

/**
 * Fetch filtered list of video classes based on search criteria
 * @param request - Video class list request with filter parameters
 * @returns Promise with array of video class list data
 */
async function GetVideoClassList(request: VideoClassListRequest): Promise<APIResponse<VideoClassListData[]>> {
  const response = await privateApi.post<VideoClassListData[]>(ApiUrls.GetVideoClassList, request);
  return response;
}

/**
 * Fetch video detail list based on filter criteria
 * @param request - Video detail request with filter parameters
 * @returns Promise with array of video detail data
 */

const methods = {
  GetChapterFilterData,
  GetVideoClassList,
};

export default methods;
