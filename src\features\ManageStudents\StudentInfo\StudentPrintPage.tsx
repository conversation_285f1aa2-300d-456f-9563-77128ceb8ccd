/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from 'react';
import { Box, Grid, Typography, Stack, Button, TextField } from '@mui/material';
import styled from 'styled-components';
import { useReactToPrint } from 'react-to-print';
import PrintIcon from '@mui/icons-material/Print';
import useAuth from '@/hooks/useAuth';
import { useTheme } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { StudentListInfo } from '@/types/StudentManagement';
import { Divider } from '@mui/material';
import { useSchool } from '@/contexts/SchoolContext';
import PaymentIcon from '@mui/icons-material/Payment';
import BackButton from '@/components/shared/BackButton';

dayjs.extend(customParseFormat);

const Root = styled(Box)`
  width: 100%;
  /* padding: 10px; */
  padding: 0px 110px;

  @media print {
    @page {
      size: A4;
      margin: 30px !important; /* Remove default print margins */
    }

    body {
      margin: 0 !important; /* Ensure no extra margin */
      padding: 0 !important;
    }

    .print-button {
      display: none !important;
    }
  }
`;

const Header = styled(Box)`
  margin-bottom: 24px;
`;

const Title = styled(Typography)`
  font-weight: bold;
  font-size: 16px;
`;

/* UPDATED → FLEX UNDERLINE */
const UnderlineBox = styled(Typography)`
  display: flex;
  flex: 1;
  border-bottom: 1px solid #000;
  font-family: Poppins Semibold;
  font-size: 14px;
  align-items: center;
`;

type FileDetail = {
  FileId: number;
  FileName: string;
  FileTitle: string;
};

type FileDetailsArray = FileDetail[];

export type AdmissionFormTypes = {
  ClassName: string;
  Surname: string;
  StudentName: string;
  FathersName: string;
  SGender: number;
  SDob: string | Dayjs;
  SdobInWords: string;
  SPlaceOfBorth: string;
  SMotherTongue: string;
  SReligion: string;
  SCaste: string;
  SNationality: string;
  SchoolLastAttended: string;
  SAddress1: string;
  FQualification: string;
  FOccupation: string;
  FCompanyYear: string;
  FOfficeAddress: string;
  FOfficeMobile: string;
  FOfficeTelephone: string;
  MQualification: string;
  MotherName: string;
  MOccupation: string;
  MCompanyYear: string;
  GuardianName: string;
  SchoolTransport: number;
  StopName: string;
  birthCertificate: string; // Store birth certificate image
  proof: string;
  RegistrationNo?: number;
};

type LocationState = {
  student?: StudentListInfo;
};

export default function StudentPrint() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const { selectedSchool } = useSchool();
  const navigate = useNavigate();

  const { state } = useLocation() as { state: LocationState };

  const student = state?.student as StudentListInfo | undefined;
  // const studentDatas = state?.data as StudentListInfo | undefined;
  const className = state?.className as string | undefined;

  const [regNoError, setRegNoError] = useState(false);
  const [regNoErrorMsg, setRegNoregNoErrorMsg] = useState('');

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId, student]);

  const location = useLocation();
  const details = location.state?.details || ''; // Access the passed details
  const [searchNumber, setSearchNumber] = useState(details);
  const [studentData, setStudentData] = useState<AdmissionFormTypes | undefined>();
  const [fileDetails, setFileDetails] = useState<FileDetailsArray>([]);
  const [loading, setLoading] = useState(false);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNumber(event.target.value);
  };

  const onSearch = async () => {
    if (!searchNumber.trim()) {
      setRegNoregNoErrorMsg('Please enter a valid registration number.');
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get(
        `https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/SearchAdmission?RegistrationNo=${searchNumber}`
      );

      console.log('API Response:', response.data);

      if (response.data?.RegistrationDetails) {
        setStudentData(response.data.RegistrationDetails);
        setFileDetails(response.data.FileDetails);
      } else {
        setRegNoError(true);
        setRegNoregNoErrorMsg('No student found with this registration number.');
        setStudentData(null);
      }
    } catch (error) {
      setRegNoError(true);
      console.error('Error fetching student data:', error);
      setRegNoregNoErrorMsg('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Detect active section on scroll
  useEffect(() => {
    onSearch();
  }, []);

  // parse SDob (string or Dayjs) into day, month name, year and compute age as on 15th June (current year)
  const getDobPartsAndAge = (sdob?: string | Dayjs) => {
    if (!sdob) return { day: '', month: '', year: '', ageAsOn15June: '' };
    let d =
      typeof sdob === 'string'
        ? dayjs(sdob, ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'DD/MM/YY'], true)
        : dayjs(sdob);
    if (!d.isValid()) d = dayjs(sdob); // fallback to loose parse
    if (!d.isValid()) return { day: '', month: '', year: '', ageAsOn15June: '' };

    const day = d.format('DD');
    const month = d.format('MMMM'); // full month name
    const year = d.format('YYYY');

    // Compute age as on 15th June of current year (adjust year if you need admission-year logic)
    const asOf = dayjs(`${dayjs().year()}-06-15`);
    let age = asOf.diff(d, 'year');
    // If birthday after asOf in the same year, diff handles it; age is integer years
    if (age < 0) age = 0;

    return { day, month, year, ageAsOn15June: String(age) };
  };

  const dobParts = getDobPartsAndAge(studentData?.SDob);

  const printRef = useRef();

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Admission Form',
    pageStyle: `
      @page { size: A4; margin: 15mm; border:1px solid #000;padding:10px; }
      body { -webkit-print-color-adjust: exact !important; }
    `,
  });

  return (
    <Box className="container">
      <Root>
        {/* PRINT BUTTON */}

        <Stack direction="row" justifyContent="space-between" alignItems="center" gap={2} p={2}>
          <BackButton onBackClick={() => navigate(-1)} title="Back" />

          <Button
            variant="contained"
            color="info"
            className="print-button"
            onClick={handlePrint}
            startIcon={<PrintIcon />}
          >
            Print
          </Button>
          {/* {student?.data} */}
        </Stack>
        {/* EVERYTHING BELOW PRINTS */}
        <Box ref={printRef}>
          {/* HEADER */}
          <Header>
            <Box flexDirection="column" display="flex" alignItems="center" justifyContent="center" mb={0}>
              <img src={selectedSchool?.schoolLogo} alt={selectedSchool?.schoolLogo} width={70} />

              {selectedSchool?.address && typeof selectedSchool.address === 'function' && selectedSchool.address()}
            </Box>
            {/* <Title>INFORMATION SHEET</Title> */}
            {/* <Typography variant="subtitle2" fontSize={15}>
              No. 306
            </Typography> */}
          </Header>
          <Title>Student Information </Title>
          <Divider sx={{ mb: 2 }} />
          <Box>
            {/* 1. NAME OF THE PUPIL */}
            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Admission Number</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.admissionNumber === '' ? '--' : student?.admissionNumber}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Student Name</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentName === '' ? '--' : student?.studentName}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Date Of Birth</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentDOB === '' ? '--' : student?.studentDOB}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Admission Date</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.admissionDate === '' ? '--' : student?.admissionDate}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Class</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.className || className || '--'}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Gender</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox> {student?.studentGender === 0 ? 'Male' : 'Female'}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Blood Group</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentBloodGroup === '' ? '--' : student?.studentBloodGroup} </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Birth Place</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentBirthPlace === '' ? '--' : student?.studentBirthPlace}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Nationality</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentNationality === '' ? '--' : student?.studentNationality}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Mother Tongue</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentMotherTongue === '' ? '--' : student?.studentMotherTongue}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Relegion</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentReligion === '' ? '--' : student?.studentReligion}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Caste</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentCaste === '' ? '--' : student?.studentCaste}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Last Studied</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentLastStudied === '' ? '--' : student?.studentLastStudied}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Permanent Address</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentPAddress === '' ? '--' : student?.studentPAddress}</UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Current Address</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentCAddress === '' ? '--' : student?.studentCAddress} </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Title>Parent Information </Title>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Father Name</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentFatherName === '' ? '--' : student?.studentFatherName} </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Father Mobile Number</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentFatherNumber === '' ? '--' : student?.studentFatherNumber}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Mother Name</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentMotherName === '' ? '--' : student?.studentMotherName} </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>
            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Mother Mobile Number</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentMotherNumber === '' ? '--' : student?.studentMotherNumber}{' '}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Father Qualification </Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentFatherQualification === '' ? '--' : student?.studentFatherQualification}{' '}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Father Occupation </Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentFatherOccupation === '' ? '--' : student?.studentFatherOccupation}{' '}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Mother Qualification </Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentMotherQualification === '' ? '--' : student?.studentMotherQualification}{' '}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Mother Occcupation</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>
                    {student?.studentMotherOccupation === '' ? '--' : student?.studentMotherOccupation}{' '}
                  </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={2} pt={1}>
              <Grid item xs={4}>
                <Typography fontSize={13}>Email Id</Typography>
              </Grid>

              <Grid item>:</Grid>
              <Grid item xs>
                <Grid item xs>
                  <UnderlineBox>{student?.studentEmailId === '' ? '--' : student?.studentEmailId} </UnderlineBox>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </Box>
        <Stack direction="row" justifyContent="center" gap={2} textAlign="center" py={2}>
          <Button
            variant="contained"
            color="info"
            className="print-button"
            onClick={handlePrint}
            startIcon={<PrintIcon />}
          >
            Print
          </Button>

          <Button
            variant="contained"
            color="success"
            className="print-button"
            onClick={() => navigate('/pay-fee-details')}
            startIcon={<PaymentIcon />}
          >
            Pay Fee
          </Button>
        </Stack>
      </Root>
    </Box>
  );
}
