import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { AssignmentDataType, AssignmentState } from '@/types/Assignment';
import { flushStore } from '../flush.slice';
import { fetchAssignmentList } from './assignment.thunks';

const initialState: AssignmentState = {
  assignmentList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const assignmentSlice = createSlice({
  name: 'assignment',
  initialState,
  reducers: {
    setAssignmentList: (state, action: PayloadAction<AssignmentDataType[]>) => {
      state.assignmentList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching manage login
      // ------ Get Admin Login List------- //
      .addCase(fetchAssignmentList.pending, (state) => {
        state.assignmentList.status = 'loading';
        state.assignmentList.error = null;
      })
      .addCase(fetchAssignmentList.fulfilled, (state, action) => {
        state.assignmentList.status = 'success';
        state.assignmentList.data = action.payload;
        state.assignmentList.error = null;
      })
      .addCase(fetchAssignmentList.rejected, (state, action) => {
        state.assignmentList.status = 'error';
        state.assignmentList.error = action.payload || 'Unknown error in fetching Assignment List';
      })

      .addCase(flushStore, () => initialState);
  },
});

export const { setAssignmentList } = assignmentSlice.actions;

export default assignmentSlice.reducer;
