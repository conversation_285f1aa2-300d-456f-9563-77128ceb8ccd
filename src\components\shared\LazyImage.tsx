import React, { useState } from 'react';
import { Box } from '@mui/material';

export default function LazyImage({ src, alt, style }: any) {
  const [loaded, setLoaded] = useState(false);

  return (
    <Box
      sx={{
        position: 'relative',
        overflow: 'hidden',
        borderRadius: '16px',
        m: .5,
      }}
    >
      {/* Blur Placeholder */}
      {!loaded && (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            inset: 0,
            background: '#f0f0f0',
            filter: 'blur(8px)',
          }}
        />
      )}

      <img
        src={src}
        alt={alt}
        loading="lazy"
        onLoad={() => setLoaded(true)}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          borderRadius: '16px',
          transition: 'transform 0.4s ease, filter 0.4s ease',
          opacity: loaded ? 1 : 0,
          ...style,
        }}
      />
    </Box>
  );
}
