import api from '@/api';
import { AssignmentDataType, AssignmentRequest } from '@/types/Assignment';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchAssignmentList = createAsyncThunk<AssignmentDataType[], AssignmentRequest, { rejectValue: string }>(
  'assignment/assignmentList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.Assignment.GetAssignmentList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Assignment List');
    }
  }
);
