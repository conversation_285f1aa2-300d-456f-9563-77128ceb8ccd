/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { LocalizationProvider, DateCalendar, PickersDay, PickersCalendarHeaderProps } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import {
  Box,
  IconButton,
  Typography,
  Stack,
  Tooltip,
  Zoom,
  Button,
  Divider,
  Paper,
  Collapse,
  tooltipClasses,
} from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
import dayjs, { Dayjs } from 'dayjs';
import { useNavigate } from 'react-router-dom'; // For redirection
import useSettings from '@/hooks/useSettings';

// Extend dayjs
dayjs.extend(isSameOrAfter);

const AnimatedTooltip = styled(({ className, ...props }: any) => (
  <Tooltip {...props} TransitionComponent={Zoom} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.chart.violet[5] : theme.palette.grey[800],
    color: theme.palette.mode === 'light' ? theme.palette.chart.violet[0] : theme.palette.grey[800],
    fontSize: '0.6875rem',
    fontFamily: 'Poppins Semibold',
    padding: '6px',
    borderRadius: '5px',
    // width:150,
    // boxShadow: theme.shadows[2],
    top: 12,
  },
  [`& .${tooltipClasses.arrow}`]: {
    display: 'none',
  },
}));

function CustomCalendarHeader(props: PickersCalendarHeaderProps<Dayjs> & { handleToday: () => void }) {
  const theme = useTheme();

  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const { currentMonth, onMonthChange, disabled, handleToday } = props;
  const handlePrev = () => onMonthChange(dayjs(currentMonth).subtract(1, 'month'), 'right');
  const handleNext = () => onMonthChange(dayjs(currentMonth).add(1, 'month'), 'left');

  return (
    <>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ px: 3.5, py: 1 }}>
        <Typography
          pl={1.7}
          variant="subtitle1"
          fontSize={14}
          sx={{ fontFamily: 'Poppins Semibold', textTransform: 'capitalize' }}
        >
          {dayjs(currentMonth).format('MMMM YYYY')}
        </Typography>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          {/* <Button variant="outlined" color="secondary" size="small" onClick={handleToday}>
            Today
          </Button> */}
          <IconButton size="small" onClick={handlePrev} disabled={disabled}>
            <KeyboardArrowLeftIcon
              sx={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
              fontSize="small"
            />
          </IconButton>
          <IconButton size="small" onClick={handleNext} disabled={disabled}>
            <KeyboardArrowRightIcon
              sx={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
              fontSize="small"
            />
          </IconButton>
        </Stack>
      </Stack>
      <Divider />
    </>
  );
}

function CustomDay({
  day,
  outsideCurrentMonth,
  events = [],
  theme,
  selectedDate,
  handleToday,
  ...other
}: {
  day: Dayjs;
  outsideCurrentMonth: boolean;
  events?: { date: string; title: string }[];
  theme: any;
  handleToday: any;
  selectedDate: Dayjs;
}) {
  const todayEvents = events.filter((e) => dayjs(e.date).isSame(day, 'day'));
  const hasEvent = todayEvents.length > 0;
  const isWeekend = day.day() === 0 || day.day() === 6;

  return (
    <AnimatedTooltip
      theme={theme}
      title={
        hasEvent ? (
          <Box width={150}>
            {todayEvents.map((e, i) => (
              <Stack direction="row" alignItems="start" gap={0}>
                {/* <Box sx={{ width: 15, height: 15, borderRadius: '50%', bgcolor: theme.palette.primary.main }} /> */}
                <Stack>
                  <Typography key={i} variant="subtitle2" fontSize="11px" fontFamily="Poppins Medium">
                    {e.title}
                  </Typography>
                  <Typography key={i} variant="caption" fontSize="10px" fontFamily="Poppins Medium">
                    {e.description}
                  </Typography>
                </Stack>
              </Stack>
            ))}
          </Box>
        ) : (
          ''
        )
      }
      placement="top"
    >
      <Box sx={{ position: 'relative' }}>
        <PickersDay
          {...other}
          day={day}
          outsideCurrentMonth={outsideCurrentMonth}
          sx={{
            color: outsideCurrentMonth
              ? theme.palette.grey[500]
              : isWeekend
              ? theme.palette.error.main
              : 'text.primary',
            fontFamily: 'Poppins Medium',
            fontSize: '0.75rem',
            alignItems: 'center',
            width: 32,
            height: 32,
            lineHeight: 1.4,
          }}
        />
        {hasEvent && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 3,
              left: '50%',
              transform: 'translateX(-50%)',
              width: 6,
              height: 6,
              borderRadius: '50%',
              backgroundColor: day.isSame(selectedDate, 'day')
                ? '#fff' // white when selected
                : theme.palette.chart.violet[1], // default violet
            }}
          />
        )}
      </Box>
    </AnimatedTooltip>
  );
}

const SmallCalendar: React.FC<{
  selectedDate: Dayjs;
  setSelectedDate: (date: Dayjs) => void;
  events?: { date: string; title: string }[];
}> = ({ selectedDate, setSelectedDate, events = [] }) => {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();

  const today = dayjs();

  // Filter upcoming events
  const upcomingEvents = [...events]
    .filter((e) => dayjs(e.date).isSameOrAfter(today, 'day'))
    .sort((a, b) => dayjs(a.date).unix() - dayjs(b.date).unix());

  return (
    <Box>
      {/* Calendar */}
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DateCalendar
          value={selectedDate}
          onChange={(newValue) => setSelectedDate(newValue || dayjs())}
          slots={{
            calendarHeader: CustomCalendarHeader,
            day: (dayProps) => <CustomDay {...dayProps} events={events} theme={theme} selectedDate={selectedDate} />,
          }}
          slotProps={{
            calendarHeader: {
              handleToday: () => setSelectedDate(dayjs()),
            },
          }}
          showDaysOutsideCurrentMonth
          dayOfWeekFormatter={(d) =>
            ({ Su: 'Sun', Mo: 'Mon', Tu: 'Tue', We: 'Wed', Th: 'Thu', Fr: 'Fri', Sa: 'Sat' }[d] || d)
          }
          sx={{
            height: 277,

            '& .MuiPickersCalendarHeader-root': { minHeight: 40 },
            '& .MuiDayCalendar-weekDayLabel': {
              fontFamily: 'Poppins Semibold',
              color: isLight ? theme.palette.common.black : theme.palette.common.white,
              fontSize: '0.75rem',
              width: 32,
              height: 'fit-content',
              mt: 1,
            },
            // '& .MuiDayCalendar-weekDay': {
            //   bgcolor: 'red',
            // },
            '& .MuiDayCalendar-weekDayLabel:nth-of-type(1), & .MuiDayCalendar-weekDayLabel:nth-of-type(7)': {
              color: theme.palette.error.main,
              fontSize: '0.75rem',
            },
          }}
        />
      </LocalizationProvider>

      {/* Today Button */}
      <Divider />
      {/* <Box sx={{ textAlign: 'center', py: 1 }}>
        <Button variant="outlined" color="secondary" size="small" onClick={() => setSelectedDate(dayjs())}>
            Today
          </Button>
      </Box> */}

      {/* Upcoming Events Header */}
      <Stack width="100%" py={0.5} pl={5} pr={4} direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="h6" fontSize={14} sx={{ fontFamily: 'Poppins Semibold' }}>
          Upcoming Events
        </Typography>
        {/* {upcomingEvents.length > 3 && ( */}
        <IconButton size="small" onClick={() => setCollapsed(!collapsed)}>
          <KeyboardArrowRightIcon
            fontSize="small"
            sx={{
              transform: collapsed ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'all 0.3s ease',
            }}
          />
        </IconButton>
        {/* )} */}
      </Stack>
      {/* <Button variant="outlined" color="secondary" size="small" onClick={() => setSelectedDate(dayjs())}>
        Today
      </Button> */}

      {/* Upcoming Events List with Collapse */}
      <Collapse in={collapsed} sx={{ px: 4 }}>
        <Stack spacing={1} sx={{ mb: 1 }}>
          {upcomingEvents.slice(0, 3).map((event) => (
            <Paper
              key={event.id}
              elevation={0}
              sx={{
                px: 1,
                py: 1,
                // borderRadius: 2,
                backgroundColor: '#fff',
                boxShadow: '0px 2px 8px rgba(0,0,0,0.05)',
                // border: 2,
                // borderColor: 'white',
              }}
            >
              <Stack direction="row" spacing={1} alignItems="center">
                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: theme.palette.primary.main }} />
                <Typography variant="body2" fontSize={12} color="text.secondary" sx={{ fontFamily: 'Poppins Medium' }}>
                  {dayjs(event.date).format('MMM DD, YYYY')} - {event.time}
                </Typography>
              </Stack>
              <Typography variant="subtitle1" fontSize={12} sx={{ fontFamily: 'Poppins Semibold' }}>
                {event.title}
              </Typography>
              <Typography variant="body2" fontSize={12} color="text.secondary" sx={{ fontFamily: 'Poppins Medium' }}>
                {event.description ?? ''}
              </Typography>
            </Paper>
          ))}
          <Stack direction="row" justifyContent="center">
            <Button
              size="small"
              endIcon={
                <ArrowForwardRoundedIcon
                  // fontSize="small"
                  sx={{
                    fontSize: '12px',
                    transition: 'transform 0.3s ease',
                    transform: 'rotate(-40deg)',
                  }}
                />
              }
              sx={{
                fontSize: 12,
                width: 'fit-content',
                '&:hover .MuiSvgIcon-root': {
                  transform: 'rotate(0deg)',
                },
              }}
              onClick={() => navigate('/school-calendar')}
            >
              View all upcoming
            </Button>
          </Stack>
        </Stack>
      </Collapse>

      {/* Collapsed Preview (show 3 items) */}
      {/* {collapsed && (
        <Stack spacing={1} sx={{ mb: 2 }}>
          {upcomingEvents.slice(0, 3).map((e) => (
            <Paper key={e.date + e.title} sx={{ p: 1, borderRadius: 1, bgcolor: '#f9f9f9' }}>
              <Typography variant="body2" color="text.secondary">
                {dayjs(e.date).format('MMM DD, YYYY')}
              </Typography>
              <Typography variant="body2">{e.title}</Typography>
            </Paper>
          ))}
          {upcomingEvents.length > 3 && (
            <Button size="small" variant="text" onClick={() => navigate('/school-calendar')}>
              View More
            </Button>
          )}
        </Stack>
      )} */}
    </Box>
  );
};

export default SmallCalendar;
