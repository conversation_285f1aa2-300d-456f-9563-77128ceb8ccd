import api from '@/api';
import { SubmitResponse } from '@/types/Common';
import {
  CheckStaffAvailabilityRequest,
  InsertTimetableRequest,
  TimetableEntryRequest,
  TimetableViewResponse,
} from '@/types/Timetable';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const checkStaffAvailability = createAsyncThunk<
  SubmitResponse,
  CheckStaffAvailabilityRequest,
  { rejectValue: string }
>('timetable/checkStaffAvailability', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Timetable.CheckStaffAvailability(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in checking staff availability');
  }
});

export const insertTimetable = createAsyncThunk<
  TimetableEntryRequest[],
  InsertTimetableRequest,
  { rejectValue: string }
>('timetable/insertTimetable', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Timetable.InsertTimetable(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in saving timetable');
  }
});

export const fetchTimetableViewByClass = createAsyncThunk<
  TimetableViewResponse,
  {
    adminId: number | undefined;
    academicId: number | undefined;
    classId: number | undefined;
  },
  { rejectValue: string }
>('timetable/fetchTimetableViewByClass', async (request, { rejectWithValue }) => {
  try {
    const response = await api.Timetable.GetTimetableViewByClass(request.adminId, request.academicId, request.classId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching timetable view');
  }
});
