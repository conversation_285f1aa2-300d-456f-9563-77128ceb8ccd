import { FetchStatus } from './Common';

export type LiveClassListRequest = {
  adminId: number;
  academicId: number;
  classId: number;
  subjectId: number;
  createdDate: string;
  status: number;
};

export type LiveClassListData = {
  liveClassId: number;
  zoomMeetingId: string;
  zoomMeetingPassword: string;
  zoomMeetingStatus: number;
  zoomMeetingLink: string;
  academicId: number;
  classId: number;
  subjectId: number;
  adminId: number;
  createdDate: string;
  updatedDate: string;
  zoomStartDate: string;
  zoomEndDate: string;
  status: number;
  className: string;
  subjectName: string;
  subjectIdSub: number;
  zoomCreatedBy: string;
  totalStudent: number;
  totalAttendStudent: number;
};

export type LiveClassAttendReportData = {
  liveAttentId: number;
  liveClassId: number;
  accademicId: number;
  studentId: number;
  studentName: string;
  className: string;
  subjectName: string;
  zoomMeetingStatus: number;
  zoomStartDate: string;
  zoomRecentDate: string;
  zoomEndDate: string;
  attentTotalTime: number;
  status: number;
  subjectId: number;
  createdBy: string;
};

export type LiveClassUnAttendReportData = {
  studentId: number;
  studentName: string;
  className: string;
  fatherName: string;
  mobileNo: string;
};

export type LiveClassState = {
  liveClassList: {
    data: LiveClassListData[];
    status: FetchStatus;
    error: string | null;
  };
  liveClassAttendReport: {
    data: LiveClassAttendReportData[];
    status: FetchStatus;
    error: string | null;
  };
  liveClassUnAttendReport: {
    data: LiveClassUnAttendReportData[];
    status: FetchStatus;
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null;
};
