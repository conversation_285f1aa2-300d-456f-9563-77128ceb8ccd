import ApiUrls from '@/config/ApiUrls';
import { SendResponse } from '@/types/Common';
import {
  LiveClassAttendReportData,
  LiveClassListData,
  LiveClassListRequest,
  LiveClassUnAttendReportData,
} from '@/types/LiveClass';
import { APIResponse } from './base/types';
import { privateApi } from './base/api';

async function GetLiveClassList(request: LiveClassListRequest): Promise<APIResponse<LiveClassListData[]>> {
  const response = await privateApi.post<LiveClassListData[]>(ApiUrls.GetLiveClassList, request);
  return response;
}

async function EndZoomLiveClass(adminId: number | undefined, liveclassId: number): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || liveclassId === undefined) {
    throw new Error('adminId and liveclassId are required');
  }
  const url = ApiUrls.EndZoomLiveClass.replace('{adminId}', adminId.toString()).replace(
    '{liveclassId}',
    liveclassId.toString()
  );
  const response = await privateApi.get<SendResponse>(url);
  return response;
}

async function DeleteZoomLiveClass(
  adminId: number | undefined,
  liveclassId: number
): Promise<APIResponse<SendResponse>> {
  if (adminId === undefined || liveclassId === undefined) {
    throw new Error('adminId and liveclassId are required');
  }
  const url = ApiUrls.DeleteZoomLiveClass.replace('{adminId}', adminId.toString()).replace(
    '{liveclassId}',
    liveclassId.toString()
  );
  const response = await privateApi.get<SendResponse>(url);
  return response;
}

async function GetLiveClassAttendReport(
  adminId: number | undefined,
  liveclassId: number
): Promise<APIResponse<LiveClassAttendReportData[]>> {
  if (adminId === undefined || liveclassId === undefined) {
    throw new Error('adminId and liveclassId are required');
  }
  const url = ApiUrls.GetLiveClassAttendReport.replace('{adminId}', adminId.toString()).replace(
    '{liveclassId}',
    liveclassId.toString()
  );
  const response = await privateApi.get<LiveClassAttendReportData[]>(url);
  return response;
}

async function GetLiveClassUnAttendReport(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined,
  liveclassId: number
): Promise<APIResponse<LiveClassUnAttendReportData[]>> {
  if (adminId === undefined || academicId === undefined || classId === undefined || liveclassId === undefined) {
    throw new Error('adminId, academicId, classId and liveclassId are required');
  }
  const url = ApiUrls.GetLiveClassUnAttendReport.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString())
    .replace('{liveclassId}', liveclassId.toString());
  const response = await privateApi.get<LiveClassUnAttendReportData[]>(url);
  return response;
}
const methods = {
  GetLiveClassList,
  EndZoomLiveClass,
  DeleteZoomLiveClass,
  GetLiveClassAttendReport,
  GetLiveClassUnAttendReport,
};

export default methods;
