/* eslint-disable no-alert */
import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CardMedia,
  Grid,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import Cropper from 'react-easy-crop';
import Slider from '@mui/material/Slider';

interface EventImageUploadProps {
  title: string;
  uploadImage: (file: File | null) => Promise<void>;
  onImageGenerated?: (image: string) => void;
  isLoading?: boolean;
  error?: string | null;
}

const EventImageUpload = ({
  title,
  uploadImage,
  onImageGenerated,
  isLoading = false,
  error = null,
}: EventImageUploadProps) => {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageURL, setImageURL] = useState<string | ''>('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [cropping, setCropping] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setUploadError(null);
    if (file && file.type.startsWith('image')) {
      setImageFile(file);
      const url = URL.createObjectURL(file);
      console.log('Image URL created:', url);
      setImageURL(url);
      setUploadedImage(url);
      setCropping(true);
    } else {
      setUploadError('Please upload a valid image file.');
    }
  };

  const handleCropComplete = useCallback(
    (_: any, croppedAreaPixel: { x: number; y: number; width: number; height: number }) => {
      setCroppedAreaPixels(croppedAreaPixel);
    },
    []
  );

  const generateCroppedImage = useCallback(async () => {
    if (uploadedImage && croppedAreaPixels) {
      const canvas = document.createElement('canvas');
      const img = new Image();
      img.src = uploadedImage;

      img.onload = async () => {
        const ctx = canvas.getContext('2d');
        canvas.width = croppedAreaPixels.width;
        canvas.height = croppedAreaPixels.height;

        if (ctx) {
          ctx.drawImage(
            img,
            croppedAreaPixels.x,
            croppedAreaPixels.y,
            croppedAreaPixels.width,
            croppedAreaPixels.height,
            0,
            0,
            croppedAreaPixels.width,
            croppedAreaPixels.height
          );

          const croppedImage = canvas.toDataURL('image/jpeg');
          setUploadError(null);
          setCropping(false);

          try {
            const res = await fetch(croppedImage);
            const blob = await res.blob();
            const file = new File([blob], 'cropped-image.jpg', { type: blob.type || 'image/jpeg' });
            await uploadImage(file);
            setImageURL(croppedImage);
            if (onImageGenerated) {
              onImageGenerated(croppedImage);
            }
          } catch (err) {
            setUploadError('Failed to upload image. Please try again.');
            console.error('Image upload error:', err);
          }
        }
      };
    }
  }, [uploadedImage, croppedAreaPixels, uploadImage, onImageGenerated]);

  return (
    <Box sx={{ p: 2 }}>
      <Card>
        <CardContent>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {title}
          </Typography>

          {(uploadError || error) && (
            <Box
              sx={{
                mb: 2,
                p: 2,
                backgroundColor: '#ffebee',
                border: '1px solid #ef5350',
                borderRadius: '4px',
              }}
            >
              <Typography variant="body2" color="error">
                {uploadError || error}
              </Typography>
            </Box>
          )}

          <Grid container columnSpacing={5} mt={3}>
            <Grid item lg={6}>
              <Button sx={{ mt: 5, mb: 2 }} variant="contained" component="label" startIcon={<CloudUploadIcon />}>
                Upload Image
                <input hidden accept="image/*" type="file" onChange={handleImageUpload} />
              </Button>
            </Grid>

            <Grid item lg={6}>
              {imageFile && <Typography variant="body1">Selected Image: {imageFile?.name}</Typography>}
            </Grid>

            <Grid item lg={6}>
              {imageURL && typeof imageURL === 'string' && (
                <Box>
                  <CardMedia
                    component="img"
                    image={imageURL}
                    alt="Event Image Preview"
                    sx={{ height: '300px', width: '100%', objectFit: 'cover', border: '1px solid #ccc' }}
                  />
                </Box>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {cropping && uploadedImage && (
        <Dialog open={cropping} onClose={() => setCropping(false)}>
          <DialogTitle>Crop Image</DialogTitle>
          <DialogContent>
            <Box sx={{ position: 'relative', width: 400, height: 400 }}>
              <Cropper
                image={uploadedImage}
                crop={crop}
                zoom={zoom}
                aspect={16 / 9}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={handleCropComplete}
              />
            </Box>
            <Slider
              value={zoom}
              min={1}
              max={3}
              step={0.1}
              onChange={(_e, newValue) => setZoom(typeof newValue === 'number' ? newValue : zoom)}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCropping(false)}>Cancel</Button>
            <Button onClick={generateCroppedImage} variant="contained" color="primary">
              Crop
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default EventImageUpload;
