import React from 'react';
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography,
  Box,
  Drawer,
  useMediaQuery,
  useTheme,
  IconButton,
  Stack,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { getInitials } from './Social';

interface LikesNotificationsProps {
  setSelectedPost: (post: any) => void;
  setOpenGallery: (value: boolean) => void;
  setOpenComments: (value: boolean) => void;
  likeNotifications: any[];
  setShowLikesNotifications: (value: boolean) => void;
  showLikesNotifications: boolean;
  posts: any[];
}

export const LikesNotificationsDialog: React.FC<LikesNotificationsProps> = ({
  setSelectedPost,
  setOpenGallery,
  setOpenComments,
  likeNotifications,
  setShowLikesNotifications,
  showLikesNotifications,
  posts,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleClose = () => setShowLikesNotifications(false);

  const Header = (
    <Box
      sx={{
        position: 'sticky',
        top: 0,
        zIndex: 10,
        bgcolor: theme.palette.background.paper,
        px: 2,
        py: 1.5,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: `1px solid ${theme.palette.divider}`,
      }}
    >
      <Typography variant="subtitle2" fontFamily="Poppins Semibold">
        Notifications
      </Typography>
      <IconButton onClick={handleClose} size="small">
        <CloseIcon sx={{ color: theme.palette.text.primary }} />
      </IconButton>
    </Box>
  );

  const Content = (
    <Box sx={{ width: isMobile ? '100%' : 'auto' }}>
      {Header}
      <DialogContent
        dividers
        sx={{
          p: 0,
          maxHeight: isMobile ? '100vh' : '70vh',
          overflowY: 'auto',
          bgcolor: theme.palette.background.default,
        }}
      >
        {likeNotifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="text.secondary">No recent likes</Typography>
          </Box>
        ) : (
          <List disablePadding>
            {likeNotifications.map((n) => (
              <React.Fragment key={n.id}>
                <ListItem
                  button
                  onClick={() => {
                    const post = posts.find((p) => p.id === n.postId) || null;
                    if (post) {
                      setSelectedPost(post);
                      setOpenGallery(Boolean(post.image));
                      setOpenComments(!post.image && post.comments.length >= 0);
                    }
                    handleClose();
                  }}
                  sx={{
                    px: 2,
                    py: 1.5,
                  }}
                >
                  <ListItemAvatar>
                    <Avatar src={n.avatar} sx={{ bgcolor: theme.palette.primary.main }}>
                      {getInitials(n.liker)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2" sx={{ fontSize: isMobile ? 12 : 14 }}>
                        <strong>{n.liker}</strong> liked <strong>{n.postOwner}</strong>&apos;s post
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary">
                        Tap to view post
                      </Typography>
                    }
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
      </DialogContent>
    </Box>
  );

  return isMobile ? (
    <Drawer
      anchor="bottom"
      open={showLikesNotifications}
      onClose={handleClose}
      PaperProps={{
        sx: {
          height: '100vh',
          overflow: 'hidden',
        },
      }}
    >
      {Content}
    </Drawer>
  ) : (
    <Dialog
      open={showLikesNotifications}
      onClose={handleClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          overflow: 'hidden',
        },
      }}
    >
      {Content}
    </Dialog>
  );
};
