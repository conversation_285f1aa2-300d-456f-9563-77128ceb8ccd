import { FetchStatus } from './Common';

// Chapter Filter Data
export type ChapterFilterData = {
  chapterId: number;
  chapterName: string;
};

// Video Class List Request
export type VideoClassListRequest = {
  adminId: number;
  academicId: number | undefined;
  classId: number;
  subjectId: number;
  chapterId: number;
  youtubeTitle: string;
  createdDate: string;
  status: number;
  ogFileStatus: number;
};

// Video Class List Data
export type VideoClassListData = {
  youtubeId: number;
  youtubeTitle: string;
  youtubeDescription: string;
  youtubeLink: string;
  youtubeSortOrder: number | null;
  youtubeDate: string;
  youtubeCreatedBy: string;
  youtubeStatus: number;
  originalFile: string;
  classId: number;
  academicId: number;
  className: string;
  subjectName: string;
  academicTime: string;
  chapterName: string;
};

// State type for OnlineVideoClass
export type OnlineVideoClassState = {
  chapterFilterData: {
    data: ChapterFilterData[];
    status: FetchStatus;
    error: string | null;
  };
  videoClassList: {
    data: VideoClassListData[];
    status: FetchStatus;
    error: string | null;
  };
  submitting: boolean;
  error: string | null;
};
