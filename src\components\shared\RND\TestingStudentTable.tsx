import React, { useState, useMemo } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Checkbox,
  Paper,
  Typography,
} from '@mui/material';

const StudentDataTable = () => {
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selected, setSelected] = useState([]);
  const [students, setStudents] = useState([
    { id: 1, name: '<PERSON><PERSON>', className: '10-A', age: 15 },
    { id: 2, name: '<PERSON><PERSON>', className: '9-B', age: 14 },
    { id: 3, name: '<PERSON>', className: '8-C', age: 13 },
    { id: 4, name: '<PERSON>', className: '10-B', age: 15 },
    { id: 5, name: '<PERSON><PERSON><PERSON>', className: '7-A', age: 12 },
    { id: 6, name: '<PERSON>', className: '9-A', age: 14 },
  ]);

  // -------------------- FILTER LOGIC --------------------
  const filteredStudents = useMemo(() => {
    return students.filter((s) => s.name.toLowerCase().includes(search.toLowerCase()));
  }, [students, search]);

  // -------------------- ROW SELECT --------------------
  const handleSelect = (id) => {
    setSelected((prev) => (prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]));
  };

  const handleSelectAll = (checked) => {
    setSelected(checked ? filteredStudents.map((s) => s.id) : []);
  };

  // -------------------- EDITABLE FIELD --------------------
  const handleEdit = (id, field, value) => {
    setStudents((prev) => prev.map((s) => (s.id === id ? { ...s, [field]: value } : s)));
  };

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Student Data Table
      </Typography>

      {/* 🔍 Search Box */}
      <TextField
        label="Search Student"
        variant="outlined"
        fullWidth
        size="small"
        sx={{ mb: 2 }}
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      {/* TABLE */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selected.length > 0 && selected.length === filteredStudents.length}
                  indeterminate={selected.length > 0 && selected.length < filteredStudents.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Class</TableCell>
              <TableCell>Age</TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {filteredStudents.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((student) => (
              <TableRow key={student.id}>
                <TableCell padding="checkbox">
                  <Checkbox checked={selected.includes(student.id)} onChange={() => handleSelect(student.id)} />
                </TableCell>

                {/* Editable Name */}
                <TableCell>
                  <TextField
                    value={student.name}
                    size="small"
                    onChange={(e) => handleEdit(student.id, 'name', e.target.value)}
                  />
                </TableCell>

                {/* Editable Class */}
                <TableCell>
                  <TextField
                    value={student.className}
                    size="small"
                    onChange={(e) => handleEdit(student.id, 'className', e.target.value)}
                  />
                </TableCell>

                <TableCell>{student.age}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* PAGINATION */}
      <TablePagination
        component="div"
        count={filteredStudents.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(e, newPage) => setPage(newPage)}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
      />
    </Paper>
  );
};

export default StudentDataTable;
