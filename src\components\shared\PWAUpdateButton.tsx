import { useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, Stack } from '@mui/material';
import NewUpdate from '@/assets/newUpdate.jpg';

export default function PWAUpdatePrompt() {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const showUpdate = () => setOpen(true);
    window.addEventListener('sw-update', showUpdate);
    return () => window.removeEventListener('sw-update', showUpdate);
  }, []);

  const refreshApp = () => {
    navigator.serviceWorker?.getRegistration().then((reg) => {
      reg?.waiting?.postMessage({ type: 'SKIP_WAITING' });
    });
    window.location.reload();
  };

  return (
    <Dialog
      open={open}
      onClose={() => {}}
      // maxWidth="xs"
      // fullWidth
      PaperProps={{
        sx: {
          width: '300px',
          borderRadius: 10,
          padding: 2,
        },
      }}
    >
      <DialogContent sx={{ textAlign: 'center' }}>
        <Stack justifyContent="center" alignItems="center" mb={2}>
          <img src={NewUpdate} width={180} alt="New Update Available" />
        </Stack>

        <Typography textAlign="center" variant="h6" fontWeight="bold" mb={1}>
          Update Available
        </Typography>

        <Typography textAlign="center" variant="body2" color="text.secondary">
          A new version of the app is ready to update.
        </Typography>

        <Button variant="contained" color="primary" onClick={refreshApp} sx={{ mt: 3, width: '100%', borderRadius: 2 }}>
          Update Now
        </Button>
      </DialogContent>
    </Dialog>
  );
}
