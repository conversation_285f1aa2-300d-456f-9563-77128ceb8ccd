/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { Box, Stack, Typography, IconButton, Divider, Avatar, useTheme, Button, SwipeableDrawer } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { RiChat1Line } from 'react-icons/ri';
import { CommentInput, getInitials, Post } from './Social';

interface CommentsDrawerProps {
  post: Post | null;
  open: boolean;
  onClose: () => void;
  onOpen?: () => void;
  onAddComment: (postId: number, text: string) => void;
  onAddReply?: (postId: number, commentId: number, text: string) => void;
  onOpenProfile?: (userName: string) => void;
  heightReelView?: string;
}

const CommentsDrawer: React.FC<CommentsDrawerProps> = ({
  post,
  open,
  onClose,
  onOpen,
  onAddComment,
  onAddReply,
  onOpenProfile,
  heightReelView,
}) => {
  const theme = useTheme();
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyToUser, setReplyToUser] = useState<string | null>(null);

  if (!post) return null;

  const handleAddComment = (text: string) => {
    onAddComment(post.id, text);
  };

  const handleAddReplyFromDialog = (text: string) => {
    if (replyingTo != null && onAddReply) {
      onAddReply(post.id, replyingTo, text);
    }
    setReplyingTo(null);
    setReplyToUser(null);
  };

  const handleCancelReply = () => {
    setReplyingTo(null);
    setReplyToUser(null);
  };

  return (
    <SwipeableDrawer
      anchor="bottom"
      open={open}
      onClose={onClose}
      onOpen={onOpen ?? (() => {})}
      disableBackdropTransition={false}
      disableDiscovery={false}
      ModalProps={{
        keepMounted: true,
        BackdropProps: { invisible: true }, // 👈 removes dark backdrop
      }}
      PaperProps={{
        sx: {
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          maxHeight: heightReelView ?? '80vh',
          bgcolor: 'theme.palette.background.paper',
          zIndex: 12000,
          overflow: 'hidden',
        },
      }}
      sx={{
        '& .MuiDrawer-paper': {
          transition: 'transform 0.3s ease-in-out',
        },
      }}
    >
      {/* 🔹 Pull Handle (like Instagram) */}
      <Box
        sx={{
          width: 40,
          height: 4,
          bgcolor: theme.palette.grey[400],
          borderRadius: 2,
          mt: 2,
          mx: 'auto',
        }}
      />

      {/* 🔹 Header */}
      <Box
        sx={{
          p: 1.5,
          borderBottom: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
        }}
      >
        <Typography textAlign="center" variant="subtitle2" fontFamily="Poppins Semibold">
          Comments
        </Typography>
        {/* <IconButton
          onClick={onClose}
          size="small"
          sx={{ position: 'absolute', right: 8, top: 4 }}
        >
          <CloseIcon fontSize="small" />
        </IconButton> */}
      </Box>

      {/* 🔹 Comments List */}
      <Box
        sx={{
          p: 2,
          overflowY: 'auto',
          flexGrow: 1,
          height: '50vh',
          '&::-webkit-scrollbar': { display: 'none' },
        }}
      >
        {post.comments.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 3, color: theme.palette.text.secondary }}>
            <RiChat1Line style={{ fontSize: 30, marginBottom: 4 }} />
            <Typography variant="body2">Be the first to comment on this post!</Typography>
          </Box>
        ) : (
          <Stack spacing={2}>
            {post.comments.map((comment) => (
              <Box
                key={comment.id}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0.5,
                  p: 1,
                  borderRadius: 2,
                }}
              >
                {/* 👤 Main Comment */}
                <Box display="flex" gap={1}>
                  <Avatar
                    src={`https://placehold.co/40x40/607d8b/ffffff?text=${getInitials(comment.author)}`}
                    sx={{ width: 30, height: 30, fontSize: 13 }}
                  />
                  <Box>
                    <Typography variant="subtitle2" fontSize={12} sx={{ fontFamily: 'Poppins Semibold' }}>
                      {comment.author}
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: 13, lineHeight: 1.3 }}>
                      {comment.text}
                    </Typography>

                    {/* 💬 Reply Button */}
                    <Button
                      size="small"
                      variant="text"
                      sx={{
                        textTransform: 'none',
                        fontSize: 12,
                        mt: 0.3,
                        color: theme.palette.primary.main,
                      }}
                      onClick={() => {
                        if (replyingTo === comment.id) {
                          setReplyingTo(null);
                          setReplyToUser(null);
                        } else {
                          setReplyingTo(comment.id);
                          setReplyToUser(comment.author);
                        }
                      }}
                    >
                      {replyingTo === comment.id ? 'Cancel' : 'Reply'}
                    </Button>
                  </Box>
                </Box>

                {/* 💭 Replies */}
                {comment.replies?.length > 0 && (
                  <Box pl={6} pt={0.5}>
                    {comment.replies.map((reply) => (
                      <Box key={reply.id} display="flex" gap={1} mb={0.5}>
                        <Avatar
                          src={`https://placehold.co/40x40/90a4ae/ffffff?text=${getInitials(reply.author)}`}
                          sx={{ width: 26, height: 26, fontSize: 12 }}
                        />
                        <Box display="flex" flexDirection="column" gap={0.3}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: 12 }}>
                            {reply.author}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: 12,
                              lineHeight: 1.3,
                              whiteSpace: 'pre-wrap',
                              wordBreak: 'break-word',
                            }}
                            component="span"
                          >
                            {reply.text.split(/(@\w+)/g).map((part, i) =>
                              part.startsWith('@') ? (
                                <Box
                                  key={i}
                                  component="span"
                                  onClick={() => onOpenProfile?.(part.substring(1))}
                                  sx={{
                                    color: theme.palette.info.main,
                                    fontWeight: 500,
                                    cursor: 'pointer',
                                    '&:hover': { textDecoration: 'underline' },
                                  }}
                                >
                                  {part}
                                </Box>
                              ) : (
                                <Box key={i} component="span" sx={{ color: theme.palette.text.primary }}>
                                  {part}
                                </Box>
                              )
                            )}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            ))}
          </Stack>
        )}
      </Box>

      <Divider />

      {/* 🔹 Comment Input */}
      <Box>
        {post.commentsDisabled ? (
          <Typography color="text.secondary" variant="body2" textAlign="center">
            Comments are disabled for this post.
          </Typography>
        ) : post.approved ? (
          <CommentInput
            postId={post.id}
            placeholder={replyingTo && replyToUser ? `@${replyToUser} ` : 'Add a comment...'}
            initialValue={replyingTo && replyToUser ? `@${replyToUser} ` : ''}
            replyToUser={replyToUser}
            replyingTo={replyingTo}
            autoFocus={Boolean(replyingTo)}
            onAdd={(text) => {
              if (replyingTo && onAddReply) {
                onAddReply(post.id, replyingTo, text);
                setReplyingTo(null);
                setReplyToUser(null);
              } else {
                onAddComment(post.id, text);
              }
            }}
            onCancel={handleCancelReply}
          />
        ) : null}
      </Box>
    </SwipeableDrawer>
  );
};

export default CommentsDrawer;
