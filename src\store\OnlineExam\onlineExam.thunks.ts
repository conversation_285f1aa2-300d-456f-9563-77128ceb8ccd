import api from '@/api';
import {
  DescriptiveExamDataType,
  DescriptiveExamRequest,
  ObjectiveExamDataType,
  ObjectiveExamRequest,
} from '@/types/OnlineExam';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchObjectiveExamList = createAsyncThunk<
  ObjectiveExamDataType[],
  ObjectiveExamRequest,
  { rejectValue: string }
>('onlineExam/objectiveExamList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.OnlineExam.GetObjectiveExamList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Objective Exam List');
  }
});

export const fetchDescriptiveExamList = createAsyncThunk<
  DescriptiveExamDataType[],
  DescriptiveExamRequest,
  { rejectValue: string }
>('onlineExam/descriptiveExamList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.OnlineExam.GetDescriptiveExamList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Descriptive Exam List');
  }
});
