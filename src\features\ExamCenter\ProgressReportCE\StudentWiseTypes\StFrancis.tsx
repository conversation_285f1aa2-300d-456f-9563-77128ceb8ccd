import React, { useCallback, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
  SelectChangeEvent,
  Autocomplete,
  Chip,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { ProgressReportCWCEDataType } from '@/types/ExamCenter';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import useAuth from '@/hooks/useAuth';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getMarkRegisterFiltersData,
  getMarkRegisterFiltersStatus,
  getProgressReportCWCEDataError,
  getProgressReportCWCEDataStatus,
} from '@/config/storeSelectors';
import { fetchMarkRegisterFilter, fetchProgressReportClassWiseCE } from '@/store/ExamCenter/ExamCenter.thunks';
import NoData from '@/assets/no-datas.png';

const A4Div = styled.div`
  width: 207mm;
  min-height: 297mm;
  border: 1.5px solid black;
  padding: 5mm;
  margin-bottom: 10mm;
  box-sizing: border-box;
  border-radius: 10px;
  page-break-before: always;
  page-break-after: always;
`;

const StFrancisRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function StFrancis({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [selectedStudentData, setSelectedStudentData] = useState<any[]>([]);
  const [selectedClass, setSelectedClass] = useState(-2);
  const [selectedExam, setSelectedExam] = useState(-2);
  const [selectedAcademicYear, setSelectedAcademicYear] = useState(-2);
  const [selectedStudent, setSelectedStudent] = useState<number[]>([]);
  const [progressReportCWData, setProgressReportCWData] = useState<ProgressReportCWCEDataType>({
    staffDetails: {
      staffId: 0,
      staffName: '',
      staffMobile: '',
    },
    subjectList: [],
    studentList: [],
    markList: [],
  });
  const MarkRegisterFilters = useAppSelector(getMarkRegisterFiltersData);
  const MarkRegisterFilterStatus = useAppSelector(getMarkRegisterFiltersStatus);
  const progressReportCWDataStatus = useAppSelector(getProgressReportCWCEDataStatus);
  const progressReportCWDataError = useAppSelector(getProgressReportCWCEDataError);

  const currentProgressReportSWRequest = React.useMemo(
    () => ({
      adminId,
      academicId: selectedAcademicYear,
      classId: selectedClass,
      examId: selectedExam,
    }),
    [adminId, selectedAcademicYear, selectedClass, selectedExam]
  );
  const loadProgressReportCWData = React.useCallback(
    async (request: { adminId: number; academicId: number; classId: number; examId: number }) => {
      try {
        const data = await dispatch(fetchProgressReportClassWiseCE(request)).unwrap();
        setProgressReportCWData(data);
      } catch (error) {
        console.error('Error loading list:', error);
      }
    },
    [dispatch]
  );

  const loadMarkRegisterFilters = React.useCallback(async () => {
    try {
      dispatch(fetchMarkRegisterFilter(adminId)).unwrap();
    } catch (error) {
      console.error('Error loading mark register filter:', error);
    }
  }, [adminId, dispatch]);

  React.useEffect(() => {
    if (MarkRegisterFilterStatus === 'idle') {
      loadMarkRegisterFilters();
    }
    if (progressReportCWDataStatus === 'idle') {
      loadProgressReportCWData({
        adminId,
        academicId: -2,
        classId: -2,
        examId: -2,
      });
    }
  }, [
    loadProgressReportCWData,
    progressReportCWDataStatus,
    MarkRegisterFilterStatus,
    loadMarkRegisterFilters,
    adminId,
  ]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedAcademicId = parseInt(e.target.value, 10);
    setSelectedAcademicYear(selectedAcademicId);
    loadProgressReportCWData({
      ...currentProgressReportSWRequest,
      academicId: selectedAcademicId,
    });
  };
  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClassId = parseInt(e.target.value, 10);
    setSelectedClass(selectedClassId);
    loadProgressReportCWData({
      ...currentProgressReportSWRequest,
      classId: selectedClassId,
    });
  };
  const handleExamChange = (e: SelectChangeEvent) => {
    const selectedExamId = parseInt(e.target.value, 10);
    setSelectedExam(selectedExamId);
    loadProgressReportCWData({
      ...currentProgressReportSWRequest,
      examId: selectedExamId,
    });
  };

  const handleReset = () => {
    setSelectedAcademicYear(-2);
    setSelectedClass(-2);
    setSelectedExam(-2);
    setSelectedStudent([]);
    loadProgressReportCWData({
      adminId,
      academicId: -2,
      classId: -2,
      examId: -2,
    });
  };

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Student_Wise_Progress_Report_StFrancis_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          height: 5px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTable-root {
        }
      }
    `,
  });

  return (
    <Page title="Student Wise">
      <StFrancisRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={selectedAcademicYear?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={-2}>Select Year</MenuItem>
                        {MarkRegisterFilters?.yearList.map((opt) => (
                          <MenuItem key={opt.academicId} value={opt.academicId}>
                            {opt.academicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        labelId="selectExam"
                        id="selectExamSelect"
                        value={selectedExam?.toString()}
                        onChange={handleExamChange}
                        placeholder="Select Exam"
                      >
                        <MenuItem value={-2}>Select Exam</MenuItem>
                        {MarkRegisterFilters?.examList.map((opt) => (
                          <MenuItem key={opt.examId} value={opt.examId}>
                            {opt.examName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilterSelect"
                        value={selectedClass?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                      >
                        <MenuItem value={-2}>Select Class</MenuItem>
                        {MarkRegisterFilters?.classList.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 250 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <Autocomplete
                        multiple
                        id="studentFilter"
                        options={[
                          { studentId: -1, studentName: 'Select All' },
                          ...(progressReportCWData?.studentList || []),
                        ]}
                        getOptionLabel={(option: any) => option.studentName}
                        value={
                          progressReportCWData?.studentList.filter((student) =>
                            selectedStudent.includes(student.studentId)
                          ) || []
                        }
                        onChange={(event, newValue) => {
                          const isSelectAll = newValue.some((option: any) => option.studentId === -1);
                          const studentIds = isSelectAll
                            ? progressReportCWData.studentList.map((s) => s.studentId)
                            : newValue.map((s: any) => s.studentId);
                          setSelectedStudent(studentIds);
                        }}
                        disabled={!progressReportCWData?.studentList || progressReportCWData.studentList.length === 0}
                        renderInput={(params) => <TextField {...params} placeholder="Select Students" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" onClick={handleReset} variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {selectedStudent.length > 0 && (
              <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
                <Stack spacing={2} direction="row">
                  <Box>
                    <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                      Print
                    </Button>
                  </Box>
                </Stack>
              </Box>
            )}
            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={componentRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {progressReportCWData?.studentList
                  .filter((s) => selectedStudent.includes(s.studentId))
                  .map((student) => (
                    <A4Div key={student.studentId}>
                      {progressReportCWDataError ? (
                        <Box
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                          width="100%"
                          height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 284px)' }}
                        >
                          <Stack direction="column" alignItems="center">
                            <img src={NoData} width="150px" alt="" />
                            <Typography variant="subtitle2" mt={2} color="GrayText">
                              No data found !
                            </Typography>
                          </Stack>
                        </Box>
                      ) : (
                        <>
                          <Box>
                            <Stack direction="column" justifyContent="center" alignItems="center" gap={1}>
                              <img src={holyLogo} width={70} alt="logo" />
                              <Typography variant="h4" fontSize={25} fontWeight={700}>
                                St. FRANCIS CENTRAL SCHOOL
                              </Typography>
                              <Typography variant="h6" color="GrayText" fontSize={13}>
                                (Affiliated to CBSE, New Delhi, Affiliation No.930923)
                              </Typography>
                              <Typography variant="h6" fontSize={16}>
                                Karuvappara, Kozhinjampara , Palakkad Dt., Kerala St.
                              </Typography>
                            </Stack>
                            <Box
                              my={4}
                              sx={{
                                display: 'flex',
                                justifyContent: { xs: 'start', sm: 'space-between' },
                                px: { xs: 3 },
                              }}
                              flexWrap="wrap"
                            >
                              <Stack direction="column" gap={1}>
                                <Typography variant="h6" fontSize={14}>
                                  Name: <span style={{ fontWeight: 'normal' }}>{student.studentName}</span>
                                </Typography>
                                <Typography variant="h6" fontSize={14}>
                                  Class:{' '}
                                  <span style={{ fontWeight: 'normal' }}>
                                    {
                                      MarkRegisterFilters?.classList.find((opt) => opt.classId === student.classId)
                                        ?.className
                                    }
                                  </span>
                                </Typography>
                              </Stack>
                              <Stack direction="column" gap={1}>
                                <Typography variant="h6" fontSize={14}>
                                  Academic :{' '}
                                  <span style={{ fontWeight: 'normal' }}>
                                    {' '}
                                    {
                                      MarkRegisterFilters?.yearList.find(
                                        (opt) => opt.academicId === student.accademicId
                                      )?.academicTime
                                    }
                                  </span>
                                </Typography>
                                <Typography variant="h6" fontSize={14}>
                                  Assessment :{' '}
                                  <span style={{ fontWeight: 'normal' }}>
                                    {' '}
                                    {MarkRegisterFilters?.examList.find((opt) => opt.examId === selectedExam)?.examName}
                                  </span>
                                </Typography>
                              </Stack>
                            </Box>
                          </Box>
                          <Paper className="card-table-container">
                            <TableContainer>
                              <Table>
                                <TableHead>
                                  <TableRow>
                                    <TableCell>Sl.No</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell>Out-Off Mark</TableCell>
                                    <TableCell>Marks Scored(CE1)</TableCell>
                                    <TableCell>Marks Scored(CE2)</TableCell>
                                    <TableCell>Marks Scored(CE3)</TableCell>
                                    <TableCell>Marks Scored(CE4)</TableCell>
                                    <TableCell>Marks Scored</TableCell>
                                    <TableCell>Grand Total</TableCell>
                                    <TableCell>Grade</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {progressReportCWData.subjectList.map((subject, index) => {
                                    const mark = progressReportCWData.markList.find(
                                      (m) => m.studentId === student.studentId && m.subjectId === subject.subjectId
                                    );
                                    const ce1 = mark ? parseInt(mark.periodicTest, 10) : 0;
                                    const ce2 = mark ? parseInt(mark.multipleAssesment, 10) : 0;
                                    const ce3 = mark ? parseInt(mark.portFolio, 10) : 0;
                                    const ce4 = mark ? parseInt(mark.subjectEnrichment, 10) : 0;
                                    const marksScored = ce1 + ce2 + ce3 + ce4;
                                    const grandTotal = marksScored + (mark ? parseInt(mark.scoredMarkTe, 10) : 0);
                                    return (
                                      <TableRow key={subject.subjectId}>
                                        <TableCell>{index + 1}</TableCell>
                                        <TableCell>{subject.subjectName}</TableCell>
                                        <TableCell>{subject.outoffMarkCe}</TableCell>
                                        <TableCell>{ce1}</TableCell>
                                        <TableCell>{ce2}</TableCell>
                                        <TableCell>{ce3}</TableCell>
                                        <TableCell>{ce4}</TableCell>
                                        <TableCell>{marksScored}</TableCell>
                                        <TableCell>{grandTotal}</TableCell>
                                        <TableCell>{mark ? mark.grade : ''}</TableCell>
                                      </TableRow>
                                    );
                                  })}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Paper>
                        </>
                      )}
                    </A4Div>
                  ))}
              </Box>
            </Box>
          </div>

          {selectedStudent.length > 0 && (
            <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
              <Stack spacing={2} direction="row">
                <Box>
                  <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                    Print
                  </Button>
                </Box>
              </Stack>
            </Box>
          )}
        </Card>
      </StFrancisRoot>
    </Page>
  );
}

export default StFrancis;
