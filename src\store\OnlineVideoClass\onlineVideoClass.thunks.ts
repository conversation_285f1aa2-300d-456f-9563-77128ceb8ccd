import api from '@/api';
import { VideoClassListData, VideoClassListRequest, ChapterFilterData } from '@/types/OnlineVideoClass';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchVideoClassList = createAsyncThunk<
  VideoClassListData[],
  VideoClassListRequest,
  { rejectValue: string }
>('onlineVideoClass/videoClassList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.OnlineVideoClass.GetVideoClassList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching video class list');
  }
});

export const fetchChapterFilterData = createAsyncThunk<
  ChapterFilterData[],
  { adminId: number; academicId: number; classId: number; subjectId: number },
  { rejectValue: string }
>('onlineVideoClass/chapterFilterData', async ({ adminId, academicId, classId, subjectId }, { rejectWithValue }) => {
  try {
    const response = await api.OnlineVideoClass.GetChapterFilterData(adminId, academicId, classId, subjectId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching chapter filter data');
  }
});
