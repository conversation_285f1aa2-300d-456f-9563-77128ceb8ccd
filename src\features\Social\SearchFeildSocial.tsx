import React from 'react';
import { TextField, InputAdornment } from '@mui/material';
import { FiSearch } from 'react-icons/fi';
import { useTheme } from '@mui/material';

const RoundedSearchField = ({
  value,
  autoFocus,
  onChange,
  onClick,
  border,
  placeholder = 'Search...',
  inputRef,
  width,
  ...props
}: any) => {
  const theme = useTheme();

  return (
    <TextField
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      size="small"
      variant="outlined"
      onClick={onClick}
      autoFocus={autoFocus}
      {...props}
      inputRef={inputRef} // ✅ forward ref to TextField
      sx={{
        width: width ?? '100%',
        '& .MuiOutlinedInput-root': {
          borderRadius: '50px', // 🟢 round corners
          border, // 🟢 round corners
          backgroundColor: theme.palette.common.white, // subtle background
          paddingRight: '20px',
          transition: 'all 0.3s ease',
          '& fieldset': {
            borderColor: 'transparent',
          },
          '&:hover fieldset': {
            borderColor: theme.palette.common.white,
          },
          '&.Mui-focused fieldset': {
            borderColor: theme.palette.common.white, // theme primary color
          },
        },
        '& input': {
          padding: '7px 0px',
        },
      }}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <FiSearch style={{ marginLeft: -5 }} fontSize="large" color={theme.palette.grey[600]} />
          </InputAdornment>
        ),
      }}
    />
  );
};

export default RoundedSearchField;
