import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/SunriseLogo.png';

const A4Div = styled.div`
  width: 207mm;
  min-height: 297mm;
  border: 1.5px solid black;
  padding: 5mm;
  margin-bottom: 10mm;
  box-sizing: border-box;
  border-radius: 10px;
  page-break-before: always;
  page-break-after: always;
`;

type StuddentWiseProgrssReport = {
  subject: string;
  maxMarks: number | string | null;
  minMarks: number | string | null;
  semester1: number | string | null;
  semester2: number | string | null;
  remarks1: number | string | null;
  remarks2: number | string | null;
};

const dummyData: StuddentWiseProgrssReport[] = [
  {
    subject: 'ENGLISH',
    maxMarks: 100,
    minMarks: 35.0,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 67,
    semester2: null,
  },
  { subject: 'HINDI', maxMarks: 50, minMarks: 17.5, remarks1: 'PASS', remarks2: null, semester1: 43, semester2: null },
  {
    subject: 'SANSKRIT',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 36,
    semester2: null,
  },
  {
    subject: 'HINDI/SANSKRIT',
    maxMarks: 100,
    minMarks: 35,
    remarks1: null,
    remarks2: null,
    semester1: 79,
    semester2: 0,
  },
  {
    subject: 'MARATHI',
    maxMarks: 100,
    minMarks: 35.0,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 72,
    semester2: null,
  },
  {
    subject: 'LANGUAGE TOTAL',
    maxMarks: 300,
    minMarks: 105,
    remarks1: null,
    remarks2: null,
    semester1: 218,
    semester2: 0,
  },
  {
    subject: 'MATHS 1',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 36,
    semester2: null,
  },
  {
    subject: 'MATHS 2',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 19,
    semester2: null,
  },
  { subject: 'MATHS', maxMarks: 100, minMarks: 35, remarks1: null, remarks2: null, semester1: 55, semester2: 0 },
  {
    subject: 'SCIENCE 1',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 35,
    semester2: null,
  },
  {
    subject: 'SCIENCE 2',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 33,
    semester2: null,
  },
  {
    subject: 'SCIENCE & TECHNOLOGY',
    maxMarks: 100,
    minMarks: 35,
    remarks1: null,
    remarks2: null,
    semester1: 68,
    semester2: 0,
  },
  {
    subject: 'HISTORY',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 30,
    semester2: null,
  },
  {
    subject: 'GEOGRAPHY',
    maxMarks: 50,
    minMarks: 17.5,
    remarks1: 'PASS',
    remarks2: null,
    semester1: 33,
    semester2: null,
  },
  {
    subject: 'SOCIAL SCIENCE',
    maxMarks: 100,
    minMarks: 35,
    remarks1: null,
    remarks2: null,
    semester1: 63,
    semester2: 0,
  },
  // { subject: 'Total', maxMarks: 600, minMarks: 404, remarks1: 'PASS', remarks2: null, semester1: '67.33%', semester2: '0.00%' },
];

const SunriseRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function Sunrise({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [divCount, setDivCount] = useState(1);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();
  // const getRandomNumber = () => Math.floor(Math.random() * 1000000);

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Student_Wise_Progress_Report_Sunrise_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          height: 3px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTable-root {
        }
      }
    `,
  });

  const getRowKey = useCallback((row: StuddentWiseProgrssReport, index) => index, []);

  const progressListColumns = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'SUBJECTS',
      },
      {
        name: 'maxMarks',
        dataKey: 'maxMarks',
        headerLabel: 'MAXIMUM MARKS',
      },
      {
        name: 'minMarks',
        dataKey: 'minMarks',
        headerLabel: 'MINIMUM MARKS',
      },
      {
        name: 'semester1',
        dataKey: 'semester1',
        headerLabel: '',
      },
      {
        name: 'remarks1',
        dataKey: 'remarks1',
        headerLabel: null,
      },
      {
        name: 'semester2',
        dataKey: 'semester2',
        headerLabel: null,
      },
      {
        name: 'remarks2',
        dataKey: 'remarks2',
        headerLabel: null,
      },
    ],
    []
  );

  return (
    <Page title="Student Wise">
      <SunriseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={componentRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {Array.from({ length: divCount }).map(() => (
                  <A4Div>
                    <Box>
                      <Stack direction="row" justifyContent="center" alignItems="center" gap={2}>
                        <img src={holyLogo} width={80} alt="logo" />
                        <Stack direction="column" justifyContent="center" alignItems="center" gap={0.5}>
                          <Typography variant="h4" fontSize={16}>
                            TELUGU SAMAJ MANDALAM’s (Reg)
                          </Typography>
                          <Typography variant="h4" fontSize={19} fontWeight={700}>
                            NEW SUNRISE ENGLISH HIGH SCHOOL
                          </Typography>
                          <Typography variant="h5" fontSize={9}>
                            Behind Mayureshwar Temple, Desalepada, Bhopar, Dombivli (E) Taluka, Kalyan Dist: Thane
                          </Typography>
                          <Typography variant="h6" fontSize={9}>
                            GOVERNMENT RECOGNISED
                          </Typography>
                          <Typography variant="h5" fontSize={9}>
                            SSC Index No: S-16.17.206 MSS/HSS Index No. 14.15.445
                          </Typography>
                          <Typography variant="h5" fontSize={9}>
                            Primary Udise Code: 27210507111 Secondary Udise Code: 27210508407
                          </Typography>
                        </Stack>
                      </Stack>
                      <Divider sx={{ borderWidth: 1.5, backgroundColor: 'black' }} />
                      <Typography variant="h4" fontSize={16} py={2} align="center">
                        1st SEMESTER PROGRESS REPORT FOR THE ACADEMIC YEAR 2024-2025
                      </Typography>
                      <Box
                        my={4}
                        sx={{ display: 'flex', justifyContent: { xs: 'start', sm: 'space-between' }, px: { xs: 3 } }}
                        flexWrap="wrap"
                      >
                        <Stack direction="column" gap={1}>
                          <Typography variant="h6" fontSize={12}>
                            Name: <span style={{ fontWeight: 'normal' }}>MUHAMMED ANSAR A</span>
                          </Typography>
                          <Typography variant="h6" fontSize={12}>
                            Class: <span style={{ fontWeight: 'normal' }}>I-A</span>
                          </Typography>
                        </Stack>
                        <Stack direction="column" gap={1}>
                          <Typography variant="h6" fontSize={12}>
                            Academic : <span style={{ fontWeight: 'normal' }}> 2023-2024</span>
                          </Typography>
                          <Typography variant="h6" fontSize={12}>
                            Assessment : <span style={{ fontWeight: 'normal' }}> Term-2</span>
                          </Typography>
                        </Stack>
                      </Box>
                    </Box>
                    <Paper className="card-table-container">
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              {progressListColumns.map((column) => (
                                <React.Fragment key={column.dataKey}>
                                  {column.headerLabel !== null && (
                                    <TableCell
                                      align="center"
                                      sx={{
                                        height: 3,
                                        fontSize: 12,
                                        border: `1px solid ${theme.palette.secondary.light}`,
                                      }}
                                      colSpan={column.dataKey === 'semester1' ? 4 : 1}
                                      rowSpan={column.dataKey === 'semester1' ? 1 : 2}
                                    >
                                      {column.dataKey === 'semester1' ? 'Marks Obtained' : column.headerLabel}
                                    </TableCell>
                                  )}
                                </React.Fragment>
                              ))}
                            </TableRow>
                            <TableRow>
                              <TableCell
                                align="center"
                                sx={{ height: 3, fontSize: 12, border: `1px solid ${theme.palette.secondary.light}` }}
                              >
                                SEMESTER 1
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ height: 3, fontSize: 12, border: `1px solid ${theme.palette.secondary.light}` }}
                              >
                                REMARKS
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ height: 3, fontSize: 12, border: `1px solid ${theme.palette.secondary.light}` }}
                              >
                                SEMESTER 2
                              </TableCell>
                              <TableCell
                                align="center"
                                sx={{ height: 3, fontSize: 12, border: `1px solid ${theme.palette.secondary.light}` }}
                              >
                                REMARKS
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dummyData.map((row, index) => (
                              <TableRow key={getRowKey(row, index)}>
                                {progressListColumns.map((column) => (
                                  <TableCell
                                    key={column.dataKey}
                                    align="center"
                                    sx={{
                                      height: 3,
                                      fontWeight: row.semester2 === 0 || row.subject === 'Total' ? 700 : 100,
                                      fontSize: row.subject === 'Total' ? 16 : 11,
                                      border: `1px solid ${theme.palette.secondary.light}`,
                                    }}
                                  >
                                    {row[column.dataKey as keyof StuddentWiseProgrssReport]}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                            <TableRow>
                              <TableCell colSpan={7} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                            <TableRow>
                              {[
                                'Total',
                                dummyData.reduce(
                                  (acc, row) =>
                                    acc + (typeof row.maxMarks === 'number' && row.semester2 !== 0 ? row.maxMarks : 0),
                                  0
                                ),
                                '',
                                `${(
                                  dummyData.reduce(
                                    (acc, row) =>
                                      acc +
                                      (typeof row.semester1 === 'number' && row.semester2 !== 0 ? row.semester1 : 0),
                                    0
                                  ) / 6
                                ).toFixed(2)}%`,
                                '',
                                '0.00%',
                                '',
                              ].map((value) => (
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 3,
                                    fontWeight: 700,
                                    fontSize: 13,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                  }}
                                >
                                  {value}
                                </TableCell>
                              ))}
                            </TableRow>
                            <TableRow>
                              <TableCell colSpan={5} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                            {[
                              {
                                subject: 'W E',
                                maxMarks: undefined,
                                minMarks: undefined,
                                semester1: 'B1',
                                semester2: null,
                              },
                              {
                                subject: 'ARTS',
                                maxMarks: undefined,
                                minMarks: undefined,
                                semester1: 'B1',
                                semester2: null,
                              },
                              {
                                subject: 'PHYSICAL EDUCATION',
                                maxMarks: undefined,
                                minMarks: undefined,
                                semester1: 'B1',
                                semester2: null,
                              },
                            ].map((row, index) => (
                              <TableRow key={getRowKey(row, index)}>
                                {progressListColumns.map(
                                  (column) =>
                                    row[column.dataKey as keyof StuddentWiseProgrssReport] !== undefined && (
                                      <TableCell
                                        colSpan={column.dataKey === 'subject' ? 3 : 2}
                                        key={column.dataKey}
                                        align={column.dataKey === 'subject' ? 'left' : 'center'}
                                        sx={{
                                          height: 3,
                                          fontWeight: 700,
                                          fontSize: 12,
                                          border: `1px solid ${theme.palette.secondary.light}`,
                                        }}
                                      >
                                        {row[column.dataKey as keyof StuddentWiseProgrssReport]}
                                      </TableCell>
                                    )
                                )}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <Box pt={3}>
                      <Typography variant="h4" fontSize={12} pb={2} align="center" fontWeight={700}>
                        INTERPRETATION OF GRADES
                      </Typography>
                      <Stack direction="row" gap={1} justifyContent="space-around" px="10%">
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          80-100: A
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          60-79: B
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          40-59: C
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          35-39: D
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          0-34: E
                        </Typography>
                      </Stack>
                    </Box>
                    <Box pt={3}>
                      <Typography variant="h5" fontSize={12} pb={1}>
                        <span style={{ fontWeight: 'bold' }}>Attendance: </span> 0/0
                      </Typography>
                      <Typography variant="h5" fontSize={12} pb={1}>
                        <span style={{ fontWeight: 'bold' }}> Pass and Promoted to : </span> - A
                      </Typography>
                      <Typography variant="h5" fontSize={12} pb={1}>
                        <span style={{ fontWeight: 'bold' }}> School Re-opens on : </span> ____ June,2024 at ____
                      </Typography>
                      <Typography variant="h5" fontSize={16} pt={1}>
                        <span style={{ fontWeight: 'bold' }}>Signature: </span>
                      </Typography>
                      <Stack direction="row" gap={1} pt={7} justifyContent="space-around">
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          Parent
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          Teacher
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          Supervisor
                        </Typography>
                        <Typography variant="h5" fontSize={12} fontWeight={700}>
                          H.M.
                        </Typography>
                      </Stack>
                    </Box>
                  </A4Div>
                ))}
              </Box>
            </Box>
          </div>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Box>
                <Button
                  variant="contained"
                  disabled={divCount === 0}
                  onClick={() => setDivCount(divCount - 1)}
                  sx={{ ml: 2 }}
                >
                  -
                </Button>
                <TextField
                  type="number"
                  value={divCount}
                  onChange={(e) => setDivCount(Number(e.target.value))}
                  sx={{ mx: 2 }}
                />
                <Button variant="contained" onClick={() => setDivCount(divCount + 1)}>
                  +
                </Button>
                <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                  Print
                </Button>
              </Box>
            </Stack>
          </Box>
        </Card>
      </SunriseRoot>
    </Page>
  );
}

export default Sunrise;
