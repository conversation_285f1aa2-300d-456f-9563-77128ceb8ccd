/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import {
  Autocomplete,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Box,
  Typography,
  Card,
  Tooltip,
  SelectChangeEvent,
  Popper,
  Chip,
  IconButton,
  Collapse,
  FormControl,
  Select,
  MenuItem,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { TimetableByDayProps } from '@/config/TimetableData';
import { TimetableSubjectProps } from '@/types/Timetable';
import { TimetableIconMap } from '@/config/TimetableIconMap';
import { MdAdd } from 'react-icons/md';
import useSettings from '@/hooks/useSettings';
import React, { FormEvent, useCallback, useMemo, useState, useEffect } from 'react';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import {
  getClassData,
  getYearData,
  getTimetableViewStatus,
  getTimetableViewData,
  getCTSFilterListStatus,
  getCTSDetailsListData,
  getCTSStaffList,
  getCTSSubjectList,
} from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList, fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import { fetchTimetableViewByClass, insertTimetable, checkStaffAvailability } from '@/store/Timetable/timetable.thunks';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { fetchCTSFilter, fetchCTSList } from '@/store/StaffMangement/staffMangement.thunks';
import useAuth from '@/hooks/useAuth';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import SaveIcon from '@mui/icons-material/Save';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import EditOffIcon from '@mui/icons-material/EditOff';
import english from '@/assets/timetable/English.svg';
import mathematics from '@/assets/timetable/Math.svg';
import physics from '@/assets/timetable/Physics.svg';
import biology from '@/assets/timetable/Biology.svg';
import social from '@/assets/timetable/Social.svg';
import arabic from '@/assets/timetable/Arabic.svg';
import chemistry from '@/assets/timetable/Chemistry.svg';
import computer from '@/assets/timetable/Computer.svg';
import malayalam from '@/assets/timetable/Malayalam.svg';
import hindi from '@/assets/timetable/Hindi.svg';
import economics from '@/assets/timetable/Economics.svg';
import accountancy from '@/assets/timetable/Accountancy.svg';
import thamil from '@/assets/timetable/Thamil.svg';
import business from '@/assets/timetable/Business.svg';
import general from '@/assets/timetable/General.svg';
import marathi from '@/assets/timetable/Marathi.svg';

const TimetableClassRoot = styled.div`
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;
      }
    }
  }
`;

const CustomPopper = (props: any) => (
  <Popper
    {...props}
    placement="bottom-start"
    modifiers={[
      {
        name: 'offset',
        options: {
          offset: [0, 4],
        },
      },
    ]}
    style={{ width: 200 }}
  />
);

type Subject = {
  id: number;
  name: string;
  teacher: string;
  icon?: string;
  subjectId?: number;
  staffId?: number;
  staffName?: string;
  hasConflict?: boolean; // Flag to track if this entry has a staff conflict
};
type SelectedSubjectsMap = Record<string, Subject>;
type EditCellState = { day: string; periodIndex: number } | null;

// Map of subject names to their icon keys for the predefined subjects
const SUBJECT_ICON_MAP: Record<string, string> = {
  English: 'english',
  Malayalam: 'malayalam',
  Arabic: 'arabic',
  Hindi: 'hindi',
  Math: 'math',
  Biology: 'biology',
  Physics: 'physics',
  Social: 'social',
  Business: 'business',
  General: 'general',
  Computer: 'computer',
  Economics: 'economics',
  Accountancy: 'accountancy',
  Chemistry: 'chemistry',
  Tamil: 'tamil',
  Marathi: 'marathi',
};

export const TimetableIcon = (props: any) => {
  const { Icon } = props;
  const icon = Icon ? TimetableIconMap[Icon] : null;
  return <img src={icon} alt={icon} />;
};
function TimetableClass() {
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;

  const [showFilter, setShowFilter] = useState(true);
  const [academicYearFilter, setAcademicYearFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const [selectedSubjectsByPeriod, setSelectedSubjectsByPeriod] = useState<SelectedSubjectsMap>({});
  const [editCell, setEditCell] = useState<EditCellState>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isSavingAll, setIsSavingAll] = useState(false);
  const [generatedColors, setGeneratedColors] = useState<Record<string, { bg: string; text: string; icon: string }>>(
    {}
  );
  const [modifiedCells, setModifiedCells] = useState<Set<string>>(new Set());
  const [validatingCell, setValidatingCell] = useState<{ day: string; periodIndex: number } | null>(null);

  const YearData = useAppSelector(getYearData);
  const classListData = useAppSelector(getClassData);
  const timetableViewStatus = useAppSelector(getTimetableViewStatus);
  const timetableViewData = useAppSelector(getTimetableViewData);
  const ctsDetailsData = useAppSelector(getCTSDetailsListData);
  const staffList = useAppSelector(getCTSStaffList);
  const subjectList = useAppSelector(getCTSSubjectList);

  // Confirmation dialog hook
  const { confirm } = useConfirm();

  // Fetch year and class lists on component mount
  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassList(adminId));
    dispatch(fetchCTSFilter(adminId));
  }, [dispatch, adminId]);

  // Fetch timetable data when year and class are selected
  useEffect(() => {
    if (academicYearFilter && classFilter) {
      dispatch(
        fetchTimetableViewByClass({
          adminId,
          academicId: academicYearFilter,
          classId: classFilter,
        })
      );
      // Fetch CTS details for subject-teacher mappings
      dispatch(
        fetchCTSList({
          pageNumber: 1,
          pageSize: 100,
          filters: {
            adminId,
            academicId: academicYearFilter,
            classId: classFilter,
            staffId: -1,
            subjectId: -1,
          },
        })
      );
    }
  }, [dispatch, adminId, academicYearFilter, classFilter]);

  // Validate cell edit for staff conflicts in real-time
  const validateCellEdit = async (
    day: string,
    periodIndex: number,
    staffId: number,
    staffName: string
  ): Promise<boolean> => {
    // If clearing the cell (staffId = 0), no validation needed
    if (staffId === 0) {
      return true;
    }

    // Find the day ID from the timetable data
    const dayData = timetableViewData?.dayList?.find((d: any) => d.name === day);
    const dayId = dayData?.dayId || 0;

    try {
      setValidatingCell({ day, periodIndex });

      // Call the API to check staff availability
      const result = await dispatch(
        checkStaffAvailability({
          staffId,
          dayId,
          periodId: periodIndex + 1, // API expects 1-based period
          academicId: academicYearFilter,
          adminId,
        })
      );

      // API returns id === 1 when staff is available (no conflict)
      const isAvailable = !!(result.payload && (result.payload as any).id === 1);

      if (!isAvailable) {
        // Conflict detected - show confirmation dialog
        const conflictMessage = (
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="body2" sx={{ mb: 2 }}>
              ⚠️ <strong>{staffName}</strong> is already assigned to another class at{' '}
              <strong>
                {day} Period {periodIndex + 1}
              </strong>
              .
            </Typography>
            <Typography variant="caption" color="textSecondary">
              Do you want to proceed anyway? This will create a staff conflict.
            </Typography>
          </Box>
        );

        const userConfirmed = await confirm(conflictMessage, 'Staff Conflict Warning', {
          okLabel: 'Proceed Anyway',
          cancelLabel: 'Cancel',
        });

        setValidatingCell(null);
        return userConfirmed;
      }

      setValidatingCell(null);
      return true;
    } catch (error) {
      console.error('DEBUG: Error validating cell edit:', error);
      setValidatingCell(null);

      // On error, show a warning but allow user to proceed
      toast.warning('Could not validate staff availability. Proceeding with caution.', {
        toastId: 'validation-error',
        autoClose: 3000,
      });

      return true;
    }
  };

  const handleSelectionChange = (
    day: string,
    periodIndex: number,
    newSelected: Subject | null,
    hasConflict: boolean = false
  ) => {
    const cellKey = `${day}-${periodIndex}`;
    if (newSelected) {
      setSelectedSubjectsByPeriod((prev) => ({
        ...prev,
        [cellKey]: {
          ...newSelected,
          hasConflict: hasConflict || undefined, // Only set if true, otherwise undefined
        },
      }));
    }
    // Track this cell as modified
    setModifiedCells((prev) => {
      const updated = new Set(prev);
      if (newSelected) {
        updated.add(cellKey);
      } else {
        updated.delete(cellKey);
      }
      return updated;
    });
  };

  const handleYearChange = (e: SelectChangeEvent) => {
    const selectedYear = YearData.find((item: any) => item.accademicId === parseInt(e.target.value, 10));
    if (selectedYear) {
      setAcademicYearFilter(selectedYear.accademicId);
    }
    setClassFilter(0);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    const selectedClass = classListData.find((item) => item.classId === parseInt(e.target.value, 10));
    if (selectedClass) {
      setClassFilter(selectedClass.classId);
    }
  };

  const handleReset = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setAcademicYearFilter(0);
    setClassFilter(0);
  }, []);

  // Transform API response data into TimetableByDayProps format
  const transformedTimetableData = useMemo(() => {
    if (!timetableViewData || !timetableViewData.dayList || !timetableViewData.timeTableListS) {
      return [];
    }

    return timetableViewData.dayList.map((day) => {
      const subjects: TimetableSubjectProps[] = Array.from({ length: 8 }, (_, periodIndex) => {
        const entry = timetableViewData.timeTableListS.find(
          (item) => item.dayId === day.dayId && item.periodId === periodIndex + 1
        );

        if (entry) {
          // Map subject name to icon key
          const subjectNameToIcon: Record<string, string> = {
            English: 'english',
            Malayalam: 'malayalam',
            Arabic: 'arabic',
            Hindi: 'hindi',
            Math: 'math',
            Biology: 'biology',
            Physics: 'physics',
            Social: 'social',
            Business: 'business',
            General: 'general',
            Computer: 'computer',
            Economics: 'economics',
            Accountancy: 'accountancy',
            Chemistry: 'chemistry',
            Tamil: 'thamil',
            Marathi: 'marathi',
          };

          return {
            id: entry.subjectId,
            name: entry.subjectName,
            teacher: entry.staffName,
            icon: subjectNameToIcon[entry.subjectName] || 'general',
          };
        }

        // Return empty subject for periods with no entry
        return {
          id: 0,
          name: '',
          teacher: '',
          icon: '',
        };
      });

      return {
        day: day.name,
        subjects,
      };
    });
  }, [timetableViewData]);

  const handleSaveTimetable = useCallback(async () => {
    if (!editCell) {
      toast.error('No cell selected for editing');
      return;
    }

    const cellKey = `${editCell.day}-${editCell.periodIndex}`;
    const selectedSubject = selectedSubjectsByPeriod[cellKey];

    if (!selectedSubject) {
      toast.error('Please select a subject');
      return;
    }

    setIsSaving(true);

    try {
      // Find the day ID from the timetable data
      const dayData = timetableViewData?.dayList?.find((d: any) => d.name === editCell.day);
      const dayId = dayData?.dayId || 0;

      await dispatch(
        insertTimetable([
          {
            adminId,
            academicId: academicYearFilter,
            classId: classFilter,
            dayId,
            periodId: editCell.periodIndex + 1, // API expects 1-based period
            subjectId: selectedSubject.subjectId || selectedSubject.id,
            staffId: selectedSubject.staffId || 0,
            result: 'success',
          },
        ])
      ).unwrap();

      toast.success('Timetable updated successfully');
      setEditCell(null);
      setModifiedCells((prev) => {
        const updated = new Set(prev);
        updated.delete(cellKey);
        return updated;
      });
      setSelectedSubjectsByPeriod((prev) => {
        const updated = { ...prev };
        delete updated[cellKey];
        return updated;
      });

      // Refresh timetable data
      dispatch(
        fetchTimetableViewByClass({
          adminId,
          academicId: academicYearFilter,
          classId: classFilter,
        })
      );
    } catch (error) {
      toast.error('Failed to update timetable');
    } finally {
      setIsSaving(false);
    }
  }, [editCell, selectedSubjectsByPeriod, dispatch, adminId, academicYearFilter, classFilter, timetableViewData]);

  const handleSaveAll = useCallback(async () => {
    if (modifiedCells.size === 0) {
      toast.error('No changes to save');
      return;
    }

    setIsSavingAll(true);

    try {
      const timetableEntries = Array.from(modifiedCells)
        .map((cellKey) => {
          const [day, periodIndexStr] = cellKey.split('-');
          const periodIndex = parseInt(periodIndexStr, 10);
          const selectedSubject = selectedSubjectsByPeriod[cellKey];

          if (!selectedSubject) {
            return null;
          }

          const dayData = timetableViewData?.dayList?.find((d: any) => d.name === day);
          const dayId = dayData?.dayId || 0;

          return {
            adminId,
            academicId: academicYearFilter,
            classId: classFilter,
            dayId,
            periodId: periodIndex + 1, // API expects 1-based period
            subjectId: selectedSubject.subjectId || selectedSubject.id,
            staffId: selectedSubject.staffId || 0,
            result: 'success',
          };
        })
        .filter((entry) => entry !== null);

      if (timetableEntries.length === 0) {
        toast.error('No valid changes to save');
        return;
      }

      await dispatch(insertTimetable(timetableEntries)).unwrap();

      toast.success(`${timetableEntries.length} timetable entries updated successfully`);
      setEditCell(null);
      setModifiedCells(new Set());
      setSelectedSubjectsByPeriod({});

      // Refresh timetable data
      dispatch(
        fetchTimetableViewByClass({
          adminId,
          academicId: academicYearFilter,
          classId: classFilter,
        })
      );
    } catch (error) {
      toast.error('Failed to save all changes');
    } finally {
      setIsSavingAll(false);
    }
  }, [modifiedCells, selectedSubjectsByPeriod, dispatch, adminId, academicYearFilter, classFilter, timetableViewData]);

  const predefinedSubjectColorMap = useMemo(
    () =>
      ({
        ENGLISH: { bg: '#ecf9ff', text: '#0288d1', icon: english },
        MALAYALAM: { bg: '#fef4f7', text: '#c2185b', icon: malayalam },
        ARABIC: { bg: '#f2f7fb', text: '#1565c0', icon: arabic },
        HINDI: { bg: '#f2fbf9', text: '#2e7d32', icon: hindi },
        MATH: { bg: '#f3ebf9', text: '#6a1b9a', icon: mathematics },
        MATHS: { bg: '#f3ebf9', text: '#6a1b9a', icon: mathematics },
        MATHEMATICS: { bg: '#f3ebf9', text: '#6a1b9a', icon: mathematics },
        BIOLOGY: { bg: '#f2faeb', text: '#388e3c', icon: biology },
        PHYSICS: { bg: '#ffe8e9', text: '#d32f2f', icon: physics },
        SCIENCE: { bg: '#fff3e0', text: '#ef6c00', icon: social },
        'SOCIAL SCIENCE': { bg: '#fff1ea', text: '#ef6c00', icon: social },
        SOCIAL: { bg: '#fff1ea', text: '#ef6c00', icon: social },
        BUSINESS: { bg: '#ecf1fd', text: '#1e88e5', icon: business },
        GENERAL: { bg: '#ebf2ff', text: '#3f51b5', icon: general },
        GK: { bg: '#ebf2ff', text: '#3f51b5', icon: general },
        'GENERAL KNOWLEDGE': { bg: '#ebf2ff', text: '#3f51b5', icon: general },
        COMPUTER: { bg: '#eaf8fe', text: '#0288d1', icon: computer },
        ECONOMICS: { bg: '#fff8eb', text: '#f9a825', icon: economics },
        ACCOUNTANCY: { bg: '#ecf8f2', text: '#388e3c', icon: accountancy },
        CHEMISTRY: { bg: '#fef4ff', text: '#8e24aa', icon: chemistry },
        TAMIL: { bg: '#f5f5f5', text: '#5d4037', icon: thamil },
        MARATHI: { bg: '#fff3e0', text: '#ef6c00', icon: marathi },
      } as Record<string, { bg: string; text: string; icon: string }>),
    []
  );

  // Get color for subject - use predefined if available, otherwise generate
  const getSubjectColor = useCallback(
    (subjectName: string) => {
      if (predefinedSubjectColorMap[subjectName]) {
        return predefinedSubjectColorMap[subjectName];
      }
      return predefinedSubjectColorMap.GENERAL;
    },
    [predefinedSubjectColorMap]
  );

  // Generate subject list from CTS data with subject-teacher pairs
  const availableSubjects = useMemo(() => {
    if (!ctsDetailsData || ctsDetailsData.length === 0) {
      // Fallback to predefined subjects if no CTS data
      return Object.keys(predefinedSubjectColorMap).map((subjectName, index) => ({
        id: index + 1,
        name: subjectName,
        teacher: '',
        icon: SUBJECT_ICON_MAP[subjectName] || '',
        subjectId: 0,
        staffId: 0,
        staffName: '',
      }));
    }

    // Create a map of unique subject-staff combinations from CTS data
    const subjectStaffMap = new Map<
      string,
      { subjectId: number; subjectName: string; staffId: number; staffName: string }
    >();

    ctsDetailsData.forEach((detail: any) => {
      const key = `${detail.subjectId}-${detail.staffId}`;
      if (!subjectStaffMap.has(key)) {
        subjectStaffMap.set(key, {
          subjectId: detail.subjectId,
          subjectName: detail.subjectName,
          staffId: detail.staffId,
          staffName: detail.staffName,
        });
      }
    });

    // Convert map to array and create subject objects
    let id = 1;
    const subjects: Subject[] = [];
    subjectStaffMap.forEach((value) => {
      subjects.push({
        id: id++,
        name: value.subjectName,
        teacher: value.staffName,
        icon: SUBJECT_ICON_MAP[value.subjectName] || '',
        subjectId: value.subjectId,
        staffId: value.staffId,
        staffName: value.staffName,
      });
    });

    return subjects;
  }, [ctsDetailsData, predefinedSubjectColorMap]);

  const timetableClassListColumn: DataTableColumn<TimetableByDayProps>[] = useMemo(() => {
    const periodColumns = Array.from({ length: 8 }, (_, index) => {
      const periodNumber = index + 1;
      return {
        name: `period${periodNumber}`,
        renderHeader: () => (
          <Typography textAlign="center" variant="subtitle2" fontSize={13}>
            Period {periodNumber}
          </Typography>
        ),
        renderCell: (row: TimetableByDayProps) => {
          const subject = row.subjects?.[index];
          const subjectName = subject?.name;
          const colors = subjectName ? getSubjectColor(subjectName) : { bg: '#f5f5f5', text: '#555', icon: '' };
          const cellKey = `${row.day}-${index}`;
          const isEditing = editCell?.day === row.day && editCell?.periodIndex === index;
          const isModified = modifiedCells.has(cellKey);

          if (isEditing || isModified || !subject || subject.id === 0) {
            return (
              <FormControl fullWidth>
                <Autocomplete
                  options={availableSubjects}
                  getOptionLabel={(option) => `${option?.name || ''} (${option?.teacher || 'No Teacher'})`}
                  loading={validatingCell?.day === row.day && validatingCell?.periodIndex === index}
                  value={selectedSubjectsByPeriod[cellKey] || subject}
                  onChange={async (_event, newValue) => {
                    if (newValue) {
                      // Validate the cell edit for conflicts
                      const isValid = await validateCellEdit(
                        row.day,
                        index,
                        newValue.staffId || 0,
                        newValue.staffName || ''
                      );

                      if (isValid) {
                        // User confirmed or no conflict - update the cell
                        // Check if this was a conflict that user proceeded with
                        const dayData = timetableViewData?.dayList?.find((d: any) => d.name === row.day);
                        const dayId = dayData?.dayId || 0;

                        const result = await dispatch(
                          checkStaffAvailability({
                            staffId: newValue.staffId || 0,
                            dayId,
                            periodId: index + 1,
                            academicId: academicYearFilter,
                            adminId,
                          })
                        );

                        const isAvailable = !!(result.payload && (result.payload as any).id === 1);
                        const cellHasConflict = !isAvailable;

                        handleSelectionChange(row.day, index, newValue, cellHasConflict);
                      }
                      // If not valid, the selection is not updated (user cancelled)
                    } else {
                      // Handle clearing the cell
                      handleSelectionChange(row.day, index, null, false);
                    }
                  }}
                  isOptionEqualToValue={(option, value) => option.id === value.id && option.staffId === value.staffId}
                  renderInput={(params) => {
                    const selected = selectedSubjectsByPeriod[cellKey];
                    return (
                      <TextField
                        {...params}
                        placeholder="Choose a subject"
                        variant="standard"
                        InputProps={{
                          ...params.InputProps,
                          startAdornment:
                            selected || (subject?.name && subject?.teacher) ? (
                              <div style={{ display: 'flex', flexDirection: 'column', padding: '4px 0', width: 58 }}>
                                <Typography
                                  variant="body2"
                                  color="black"
                                  fontWeight="bold"
                                  zIndex={11}
                                  sx={{
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  }}
                                >
                                  {selected?.name || subject?.name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="black"
                                  sx={{
                                    fontSize: 12,
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  }}
                                  zIndex={11}
                                >
                                  {selected?.teacher || subject?.teacher}
                                </Typography>
                              </div>
                            ) : (
                              <Typography
                                sx={{ fontSize: 9.5, width: 58 }}
                                color="secondary"
                                whiteSpace="nowrap"
                                variant="body2"
                                zIndex={11}
                              >
                                Choose a subject
                              </Typography>
                            ),

                          disableUnderline: false,
                        }}
                        sx={{
                          borderRadius: 1,
                          '& .MuiInputBase-root': {
                            backgroundColor: isEditing ? theme.palette.warning.lighter : '',
                            height: 70,
                            paddingX: 1,
                          },
                          '& input': {
                            display: 'none', // hide default input value
                          },
                        }}
                      />
                    );
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, idx) => (
                      <Chip size="small" label={option.name} {...getTagProps({ index: idx })} />
                    ))
                  }
                  // filterSelectedOptions
                  renderOption={(props, option) => (
                    <li {...props}>
                      <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <span style={{ fontWeight: 'bold' }}>{option.name}</span>
                        <span style={{ fontSize: '0.85em', color: theme.palette.grey[500] }}>{option.teacher}</span>
                      </div>
                    </li>
                  )}
                  sx={{
                    height: 70,
                    borderRadius: 1,
                  }}
                  PopperComponent={CustomPopper}
                />
                {isEditing && (
                  <Tooltip title="Cancel Edit">
                    <IconButton sx={{ position: 'absolute', right: 0 }} size="small" onClick={() => setEditCell(null)}>
                      <EditOffIcon sx={{ fontSize: '12px' }} />
                    </IconButton>
                  </Tooltip>
                )}
              </FormControl>
            );
          }

          return (
            <Box
              sx={{
                backgroundColor: colors.bg,
                px: 0.5,
                py: 1,
                borderRadius: 1,
                height: 70,
                minWidth: 120,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative',
                overflow: 'hidden',
                cursor: 'pointer',
                '&:hover::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  height: '100%',
                  width: '100%',
                  // background: 'linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6))',
                  borderRadius: 1,
                  backgroundColor: 'black',
                  zIndex: 111,
                  opacity: 0.3,
                },
                '&:hover .delete-icon': {
                  opacity: 1,
                  // transform: 'scale(1)',
                },
                transition: 'transform 0.2s',
              }}
            >
              <Stack position="absolute" right={0} top={0}>
                <img width={25} src={colors.icon} alt={colors.icon} />
              </Stack>
              {/* Edit Icon (Hidden by default, shown on hover) */}
              <Stack
                className="delete-icon"
                position="absolute"
                zIndex={1111}
                sx={{
                  opacity: 0,
                  transition: 'opacity 0.2s',
                }}
              >
                <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
                  <Tooltip title="Edit">
                    <IconButton
                      onClick={() => setEditCell({ day: row.day, periodIndex: index })}
                      size="small"
                      color="warning"
                      sx={{
                        backgroundColor: theme.palette.warning.main,
                        '&:hover': {
                          backgroundColor: theme.palette.warning.main,
                        },
                      }}
                    >
                      <ModeEditIcon fontSize="small" sx={{ color: 'white' }} />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Stack>
              {/* Subject Name */}
              <Typography
                key={`${row.day}-period${periodNumber}`}
                sx={{ textAlign: 'center', color: colors.text }}
                variant="body2"
                fontWeight="bold"
                zIndex={2}
              >
                {subjectName || '-'}
              </Typography>

              {/* Teacher Name */}
              <Typography
                key={`${row.day}-period${periodNumber}-teacher`}
                sx={{ textAlign: 'center', fontSize: 12, color: colors.text }}
                variant="body2"
                zIndex={2}
              >
                {subject?.teacher || '-'}
              </Typography>
            </Box>
          );
        },
        sortable: false,
      };
    });

    return [
      {
        name: 'day',
        headerLabel: '',
        renderCell: (row) => (
          <Typography
            alignContent="center"
            height="100%"
            borderRight={1}
            borderColor={isLight ? theme.palette.grey[300] : theme.palette.grey[700]}
            minWidth={60}
            px={1}
            textTransform="uppercase"
            textAlign="center"
            variant="body2"
            fontWeight="bold"
          >
            {row.day}
          </Typography>
        ),
        sortable: false,
      },
      ...periodColumns,
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          const isRowBeingEdited = editCell?.day === row.day;
          return (
            <Stack
              sx={{ borderLeft: 1, borderColor: isLight ? theme.palette.grey[300] : theme.palette.grey[700] }}
              height="100%"
              width={62}
              direction="column"
              alignItems="center"
              justifyContent="center"
            >
              <IconButton
                size="small"
                color="success"
                onClick={handleSaveTimetable}
                disabled={isSaving || !isRowBeingEdited}
              >
                <SaveIcon />
              </IconButton>
              <Typography sx={{ color: theme.palette.success.main }} variant="subtitle2" fontSize={10}>
                Save
              </Typography>
            </Stack>
          );
        },
      },
    ];
  }, [
    selectedSubjectsByPeriod,
    theme,
    editCell,
    isLight,
    handleSaveTimetable,
    isSaving,
    getSubjectColor,
    availableSubjects,
    modifiedCells,
  ]);

  const getRowKey = useCallback((row: TimetableByDayProps) => row.day, []);

  // Determine what to render in the table area
  const renderTableContent = () => {
    if (!academicYearFilter || !classFilter) {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: '400px',
          }}
        >
          <Typography variant="body1" color="textSecondary">
            Please select Academic Year and Class to view timetable
          </Typography>
        </Box>
      );
    }

    if (transformedTimetableData.length === 0) {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: '400px',
          }}
        >
          <Typography variant="body1" color="textSecondary">
            No timetable data available for the selected class
          </Typography>
        </Box>
      );
    }

    return (
      <DataTable
        hoverDisable={false}
        tableStyles={{ minWidth: { xs: '1100px' } }}
        showHorizontalScroll
        columns={timetableClassListColumn}
        data={transformedTimetableData}
        getRowKey={getRowKey}
        fetchStatus={timetableViewStatus}
      />
    );
  };

  return (
    <Page title="TimetableClass">
      <TimetableClassRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Timetable Class
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
              {modifiedCells.size > 0 && (
                <Button
                  startIcon={<SaveIcon />}
                  sx={{ borderRadius: '20px', mr: 1 }}
                  variant="contained"
                  color="success"
                  size="small"
                  onClick={handleSaveAll}
                  disabled={isSavingAll}
                >
                  Save All ({modifiedCells.size})
                </Button>
              )}
              <Button
                startIcon={<MdAdd size="20px" />}
                sx={{ borderRadius: '20px', mr: 1 }}
                variant="outlined"
                size="small"
                onClick={() => navigate('/time-table/generator')}
              >
                Create
              </Button>
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Paper
              className="card-table-container"
              sx={{
                border: `1px solid #e8e8e9`,
                width: '100%',
                overflow: 'auto',
              }}
            >
              {renderTableContent()}
            </Paper>
          </div>
        </Card>
      </TimetableClassRoot>
    </Page>
  );
}

export default TimetableClass;
