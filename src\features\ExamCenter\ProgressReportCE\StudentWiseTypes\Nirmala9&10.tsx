/* eslint-disable no-nested-ternary */
import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
  Avatar,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/NirmalaLogo.png';

const A4Div = styled.div`
  width: 207mm;
  min-height: 297mm;
  border: 1.5px solid black;
  padding: 5mm;
  margin-bottom: 10mm;
  box-sizing: border-box;
  border-radius: 10px;
  page-break-before: always;
  page-break-after: always;
`;

type DummyDataType = {
  subgroup?: number | string | null;
  subject: string;
  subject2?: string | null;
  unitTest1: number | string;
  termA: number | string;
  termA2?: number | string | null;
  prelims: number | string;
  prelims2?: number | string | null;
  termAPlusPrelims: number | string | null;
  avg: number | string | null;
  [key: string]: number | string | null | undefined;
};

const dummyData: DummyDataType[] = [
  { subgroup: 0, subject: 'ENGLISH', unitTest1: 8, termA: 45, prelims: 48, termAPlusPrelims: 93, avg: 47 },
  { subgroup: 0, subject: 'MARATHI', unitTest1: 8, termA: 36, prelims: 38, termAPlusPrelims: 74, avg: 37 },
  { subgroup: 0, subject: 'HINDI', unitTest1: 8, termA: 45, prelims: 45, termAPlusPrelims: 90, avg: 45 },
  {
    subgroup: 1,
    subject: 'ALGEBRA',
    unitTest1: 8,
    termA: 30,
    prelims: 50,
    termAPlusPrelims: 107,
    avg: 54,
    subject2: 'GEOMETRY',
    unitTest2: 9,
    termA2: 20,
    prelims2: 34,
  },
  {
    subgroup: 1,
    subject: 'SCIENCE I',
    unitTest1: 7,
    termA: 31,
    prelims: 63,
    termAPlusPrelims: 124,
    avg: 62,
    subject2: 'SCIENCE II',
    unitTest2: 14,
    termA2: 32,
    prelims2: 28,
  },
  {
    subgroup: 1,
    subject: 'GEOGRAPHY',
    unitTest1: 10,
    termA: 20,
    prelims: 47,
    termAPlusPrelims: 94,
    avg: 47,
    subject2: 'HISTORY & POLITICAL SCIENCE',
    unitTest2: 15,
    termA2: 27,
    prelims2: 25,
  },
  {
    subgroup: 0,
    subject: 'TOTAL',
    unitTest1: '87/180',
    termA: '286/600',
    prelims: '296/600',
    termAPlusPrelims: '582/1200',
    avg: '292/600',
  },
  {
    subgroup: 0,
    subject: 'PERCENTAGE',
    unitTest1: '48.33%',
    termA: '47.67%',
    prelims: '49.33%',
    termAPlusPrelims: '48.67%',
    avg: null,
  },
];
const NirmalaRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function Nirmala({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [divCount, setDivCount] = useState(1);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();
  // const getRandomNumber = () => Math.floor(Math.random() * 1000000);

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Student_Wise_Progress_Report_Nirmala_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          // height: 5px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTable-root {
        }
      }
    `,
  });

  const getRowKey = useCallback((row) => row.slNo, []);

  const progressListColumns = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'unitTest1',
        dataKey: 'unitTest1',
        headerLabel: 'Unit Test 1',
      },
      {
        name: 'termA',
        dataKey: 'termA',
        headerLabel: 'Term A',
      },
      {
        name: 'prelims',
        dataKey: 'prelims',
        headerLabel: 'Prelims',
      },
      {
        name: 'termAPlusPrelims',
        dataKey: 'termAPlusPrelims',
        headerLabel: 'Term A + Prelims',
      },
      {
        name: 'avg',
        dataKey: 'avg',
        headerLabel: 'Average',
      },
    ],
    []
  );

  const gradeColumns = useMemo(
    () => [
      { name: 'subject', dataKey: 'subject', headerLabel: 'SUBJECT' },
      { name: 'grade', dataKey: 'grade', headerLabel: 'TERM A' },
      { name: 'grade2', dataKey: 'grade2', headerLabel: 'PRELIMS' },
    ],
    []
  );
  return (
    <Page title="Student Wise">
      <NirmalaRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={componentRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {Array.from({ length: divCount }).map(() => (
                  <A4Div>
                    <Box>
                      <Stack direction="row" justifyContent="center" alignItems="center" gap={4}>
                        <img src={holyLogo} width={97} alt="logo" />
                        <Stack direction="column" justifyContent="center" alignItems="center" gap={0.5}>
                          <Typography variant="h6" fontSize={14}>
                            Carmel Jyothi Education Society&apos;s
                          </Typography>
                          <Typography variant="h6" fontSize={22}>
                            NIRMALA CONVENT HIGH SCHOOL
                          </Typography>
                          <Typography variant="h5" fontSize={11}>
                            Nirmalwadi, Kharadi, Pune-411014. Maharashtra Govt. Recog. no. (Pri): VLS 1002/ (1000/2)
                          </Typography>
                          <Typography variant="h5" fontSize={11}>
                            dated 14/01/2003 Govt. Recog. no. (Sec): VLS-60733/D
                          </Typography>
                          <Typography variant="h4" fontSize={16} py={2} fontWeight={500}>
                            PROGRESS REPORT 2023-2024
                          </Typography>
                        </Stack>
                      </Stack>
                      <Divider sx={{ borderWidth: 2, backgroundColor: 'black' }} />
                      <Box
                        my={0.5}
                        sx={{
                          display: 'flex',
                          justifyContent: { xs: 'start', sm: 'space-between' },
                          px: { xs: 3 },
                          py: 1,
                        }}
                        flexWrap="wrap"
                      >
                        <Stack direction="column" gap={1}>
                          <Typography variant="h6" fontSize={14}>
                            Student Name : <span style={{ fontWeight: 'normal' }}> MUHAMMED ANSAR A</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Date of birth : <span style={{ fontWeight: 'normal' }}>: 05-04-2020</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Roll No. :<span style={{ fontWeight: 'normal' }}> 1</span>
                          </Typography>
                        </Stack>
                        <Stack direction="column" gap={1} alignItems="end">
                          <Typography variant="h6" fontSize={14}>
                            Class & Section: <span style={{ fontWeight: 'normal' }}> I-A</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Admission No. : <span style={{ fontWeight: 'normal' }}> 5839</span>
                          </Typography>
                        </Stack>
                        <Avatar src="" sx={{ width: 80, height: 80 }} />
                      </Box>
                      <Divider sx={{ borderWidth: 2, backgroundColor: 'black', mb: 3 }} />
                    </Box>
                    <Paper>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              {progressListColumns.map((column) => (
                                <TableCell
                                  align="center"
                                  key={column.dataKey}
                                  sx={{ border: `1px solid ${theme.palette.secondary.light}` }}
                                >
                                  {column.headerLabel}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dummyData.map((row) =>
                              row.subgroup === 0 ? (
                                <TableRow key={getRowKey(row)}>
                                  {progressListColumns.map((column) => (
                                    <TableCell
                                      key={column.dataKey}
                                      align="center"
                                      sx={{ border: `1px solid ${theme.palette.secondary.light}` }}
                                    >
                                      {row[column.dataKey]}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ) : (
                                <>
                                  <TableRow key={getRowKey(row)}>
                                    {progressListColumns.map((column) => (
                                      <TableCell
                                        key={column.dataKey}
                                        align="center"
                                        sx={{ border: `1px solid ${theme.palette.secondary.light}`, p: 0 }}
                                        rowSpan={column.dataKey === 'unitTest1' || column.dataKey === 'subject' ? 1 : 2}
                                      >
                                        {column.dataKey === 'termA' ? (
                                          <Box display="flex" height="100%">
                                            <Box flex={1}>
                                              <Box display="flex" flexDirection="column" height="100%">
                                                <Box
                                                  flex={1}
                                                  display="flex"
                                                  alignItems="center"
                                                  justifyContent="center"
                                                >
                                                  {row.termA}
                                                </Box>
                                                <Box
                                                  flex={1}
                                                  sx={{ borderTop: `1px solid ${theme.palette.secondary.light}` }}
                                                  display="flex"
                                                  alignItems="center"
                                                  justifyContent="center"
                                                >
                                                  {row.termA2}
                                                </Box>
                                              </Box>
                                            </Box>
                                            <Box
                                              flex={1}
                                              sx={{ borderLeft: `1px solid ${theme.palette.secondary.light}` }}
                                              display="flex"
                                              alignItems="center"
                                              justifyContent="center"
                                            >
                                              {Number(row.termA) + (row.termA2 ? Number(row.termA2) : 0)}
                                            </Box>
                                          </Box>
                                        ) : column.dataKey === 'prelims' ? (
                                          <Box display="flex" height="100%">
                                            <Box flex={1}>
                                              <Box display="flex" flexDirection="column" height="100%">
                                                <Box
                                                  flex={1}
                                                  display="flex"
                                                  alignItems="center"
                                                  justifyContent="center"
                                                >
                                                  {row.prelims}
                                                </Box>
                                                <Box
                                                  flex={1}
                                                  sx={{ borderTop: `1px solid ${theme.palette.secondary.light}` }}
                                                  display="flex"
                                                  alignItems="center"
                                                  justifyContent="center"
                                                >
                                                  {row.prelims2}
                                                </Box>
                                              </Box>
                                            </Box>
                                            <Box
                                              flex={1}
                                              sx={{ borderLeft: `1px solid ${theme.palette.secondary.light}` }}
                                              display="flex"
                                              alignItems="center"
                                              justifyContent="center"
                                            >
                                              {Number(row.prelims) + (row.prelims2 ? Number(row.prelims2) : 0)}
                                            </Box>
                                          </Box>
                                        ) : (
                                          row[column.dataKey]
                                        )}
                                      </TableCell>
                                    ))}
                                  </TableRow>
                                  <TableRow key={getRowKey(row)}>
                                    {progressListColumns.map((column) =>
                                      column.dataKey === 'unitTest1' || column.dataKey === 'subject' ? (
                                        <TableCell
                                          key={column.dataKey}
                                          align="center"
                                          sx={{ border: `1px solid ${theme.palette.secondary.light}` }}
                                        >
                                          {column.dataKey === 'subject' ? row.subject2 : row.unitTest2}
                                        </TableCell>
                                      ) : null
                                    )}
                                  </TableRow>
                                </>
                              )
                            )}
                            <TableRow>
                              <TableCell colSpan={5} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                            <TableRow>
                              {gradeColumns.map((column) => (
                                <TableCell
                                  colSpan={2}
                                  align={column.dataKey === 'subject' ? 'left' : 'center'}
                                  sx={{
                                    height: 3,
                                    fontWeight: 700,
                                    color: 'GrayText',
                                    fontSize: 12,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    pl: column.dataKey === 'subject' ? 1 : 5,
                                  }}
                                >
                                  {column.headerLabel}
                                </TableCell>
                              ))}
                            </TableRow>
                            {[
                              {
                                subject: 'W E',
                                grade: 'A',
                                grade2: 'B',
                              },
                              {
                                subject: 'ARTS',
                                grade: 'A',
                                grade2: 'B',
                              },
                              {
                                subject: 'PHYSICAL EDUCATION',
                                grade: 'A',
                                grade2: 'B',
                              },
                            ].map((row: { subject: string; grade: string }, index) => (
                              <TableRow key={getRowKey(row)}>
                                {gradeColumns.map((column) => (
                                  <TableCell
                                    colSpan={2}
                                    key={column.dataKey}
                                    align={column.dataKey === 'subject' ? 'left' : 'center'}
                                    sx={{
                                      height: 3,
                                      fontWeight: 700,
                                      fontSize: 12,
                                      border: `1px solid ${theme.palette.secondary.light}`,
                                    }}
                                  >
                                    {row[column.dataKey as keyof typeof row]}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <Paper sx={{ pt: 5 }}>
                      <TableContainer>
                        <Typography variant="h6" align="center" fontSize={16} sx={{ py: 1 }}>
                          ATTENDANCE DETAILS
                        </Typography>
                        <Table>
                          <TableRow>
                            {[' No.of Working Days', 'No.of Present', 'Attendance Percentage'].map((row) => (
                              <TableCell
                                align="center"
                                sx={{
                                  height: 7,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontWeight: 600,
                                  fontSize: 13,
                                  color: 'Grey',
                                }}
                              >
                                {row}
                              </TableCell>
                            ))}
                          </TableRow>
                          <TableBody>
                            <TableRow>
                              {[160, 148, 93].map((column) => (
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 7,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontWeight: 500,
                                    fontSize: 13,
                                  }}
                                >
                                  {column}
                                </TableCell>
                              ))}
                            </TableRow>
                            <TableRow>
                              <TableCell
                                align="left"
                                colSpan={3}
                                sx={{
                                  height: 7,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontWeight: 700,
                                  fontSize: 13,
                                }}
                              >
                                Promotion status: Passed to Std II
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <Stack direction="row" gap={1} mt={10} pt={10} justifyContent="space-around">
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Date : 07-03-2025
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Signature of Teacher
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Signature of Principal
                      </Typography>
                    </Stack>
                  </A4Div>
                ))}
              </Box>
            </Box>
          </div>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Box>
                <Button
                  variant="contained"
                  disabled={divCount === 0}
                  onClick={() => setDivCount(divCount - 1)}
                  sx={{ ml: 2 }}
                >
                  -
                </Button>
                <TextField
                  type="number"
                  value={divCount}
                  onChange={(e) => setDivCount(Number(e.target.value))}
                  sx={{ mx: 2 }}
                />
                <Button variant="contained" onClick={() => setDivCount(divCount + 1)}>
                  +
                </Button>
                <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                  Print
                </Button>
              </Box>
            </Stack>
          </Box>
        </Card>
      </NirmalaRoot>
    </Page>
  );
}

export default Nirmala;
