import { FetchStatus } from './Common';

// Assignment //
export type AssignmentRequest = {
  adminId: number;
  academicId: number | undefined;
  classId: number;
  subjectId: number;
  createdDate: string | null;
  status: number;
};

export type AssignmentDataType = {
  assignmentId: number;
  adminId: number;
  academicId: number | undefined;
  classId: number;
  subjectId: number;
  name: string;
  description: string;
  outoffMark: number;
  startDate: number;
  endDate: number;
  createdDate: number | null;
  modifiedDate: number;
  status: number;
  className: string;
  subjectName: string;
  academicTime: string;
  createdBy: string;
  totalFile: number;
  totalSubmit: number;
  totalStudent: number;
};

// Assignment State //
export type AssignmentState = {
  assignmentList: {
    status: FetchStatus;
    data: AssignmentDataType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
