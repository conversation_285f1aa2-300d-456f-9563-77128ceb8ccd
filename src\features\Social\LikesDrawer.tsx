import React from 'react';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface LikedUsersDrawerProps {
  users: { userName: string; avatar: string }[];
  open: boolean;
  onClose: () => void;
  theme: any;
}

const getInitials = (name: string) =>
  name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();

const LikedUsersDrawer: React.FC<LikedUsersDrawerProps> = ({ users, open, onClose, theme }) => {
  return (
    <Drawer
      anchor="bottom"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          maxHeight: '80vh',
          overflow: 'hidden',
          backgroundColor: theme.palette.background.paper,
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          position: 'relative',
          textAlign: 'center',
          p: 2,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography textAlign="center" variant="subtitle2" fontFamily="Poppins Semibold">
          Likes
          {/* ({users.length}) */}
        </Typography>
        {/* <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 6,
          }}
        >
          <CloseIcon />
        </IconButton> */}
      </Box>

      {/* User List */}
      <Box
        sx={{
          overflowY: 'auto',
          height: '50vh',
          '&::-webkit-scrollbar': {
            height: '0px',
            width: '0px',
          },
        }}
      >
        <List>
          {users.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              No likes yet.
            </Typography>
          ) : (
            users.map((user, index) => (
              <ListItem key={index}>
                <ListItemAvatar>
                  <Avatar src={user.avatar} sx={{ bgcolor: theme.palette.primary.main }}>
                    {getInitials(user.userName)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography variant="body1" fontWeight={500}>
                      {user.userName}
                    </Typography>
                  }
                />
              </ListItem>
            ))
          )}
        </List>
      </Box>
    </Drawer>
  );
};

export default LikedUsersDrawer;
