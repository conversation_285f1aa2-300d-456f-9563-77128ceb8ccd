import React from 'react';
import { Typography, Box } from '@mui/material';
import { useCalendar } from '@/context/CalendarContext';
import dayjs from 'dayjs';

const CalendarHeader: React.FC = () => {
  const { selectedDate } = useCalendar();
  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="h6" sx={{ fontWeight: 600 }}>
        {selectedDate ? dayjs(selectedDate).format('MMMM YYYY') : 'No date selected'}
      </Typography>
    </Box>
  );
};

export default CalendarHeader;
