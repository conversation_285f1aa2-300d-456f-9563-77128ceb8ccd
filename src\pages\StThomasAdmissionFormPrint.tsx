import React, { useEffect, useRef, useState } from 'react';
import { Box, Grid, Typography, Stack, Button, TextField } from '@mui/material';
import styled from 'styled-components';
import { useReactToPrint } from 'react-to-print';
import PrintIcon from '@mui/icons-material/Print';
import useAuth from '@/hooks/useAuth';
import { useTheme } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { useLocation } from 'react-router-dom';
import axios from 'axios';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(customParseFormat);

const Root = styled(Box)`
  width: 100%;
  /* padding: 10px; */

  @media print {
    @page {
      size: A4;
      margin: 30px !important; /* Remove default print margins */
    }

    body {
      margin: 0 !important; /* Ensure no extra margin */
      padding: 0 !important;
    }

    .print-button {
      display: none !important;
    }
  }
`;

const Header = styled(Box)`
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const Title = styled(Typography)`
  font-weight: bold;
  font-size: 25px;
`;

/* UPDATED → FLEX UNDERLINE */
const UnderlineBox = styled(Typography)`
  display: flex;
  flex: 1;
  border-bottom: 1px solid #000;
  font-family: Poppins Semibold;
  font-size: 13px;
  align-items: center;
`;

type FileDetail = {
  FileId: number;
  FileName: string;
  FileTitle: string;
};

type FileDetailsArray = FileDetail[];

export type AdmissionFormTypes = {
  ClassName: string;
  Surname: string;
  StudentName: string;
  FathersName: string;
  SGender: number;
  SDob: string | Dayjs;
  SdobInWords: string;
  SPlaceOfBorth: string;
  SMotherTongue: string;
  SReligion: string;
  SCaste: string;
  SNationality: string;
  SchoolLastAttended: string;
  SAddress1: string;
  FQualification: string;
  FOccupation: string;
  FCompanyYear: string;
  FOfficeAddress: string;
  FOfficeMobile: string;
  FOfficeTelephone: string;
  MQualification: string;
  MotherName: string;
  MOccupation: string;
  MCompanyYear: string;
  GuardianName: string;
  SchoolTransport: number;
  StopName: string;
  birthCertificate: string; // Store birth certificate image
  proof: string;
  RegistrationNo?: number;
};

export default function StThomasAdmissionFormPrint() {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();

  const [regNoError, setRegNoError] = useState(false);
  const [regNoErrorMsg, setRegNoregNoErrorMsg] = useState('');

  useEffect(() => {
    dispatch(fetchClassList(adminId));
  }, [dispatch, adminId]);

  const location = useLocation();
  const details = location.state?.details || ''; // Access the passed details
  const [searchNumber, setSearchNumber] = useState(details);
  const [studentData, setStudentData] = useState<AdmissionFormTypes | undefined>();
  const [fileDetails, setFileDetails] = useState<FileDetailsArray>([]);
  const [loading, setLoading] = useState(false);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchNumber(event.target.value);
  };

  const onSearch = async () => {
    if (!searchNumber.trim()) {
      setRegNoregNoErrorMsg('Please enter a valid registration number.');
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get(
        `https://thereseregapi.pasdaily.in/ElixirApi/StudentSet/SearchAdmission?RegistrationNo=${searchNumber}`
      );

      console.log('API Response:', response.data);

      if (response.data?.RegistrationDetails) {
        setStudentData(response.data.RegistrationDetails);
        setFileDetails(response.data.FileDetails);
      } else {
        setRegNoError(true);
        setRegNoregNoErrorMsg('No student found with this registration number.');
        setStudentData(null);
      }
    } catch (error) {
      setRegNoError(true);
      console.error('Error fetching student data:', error);
      setRegNoregNoErrorMsg('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Detect active section on scroll
  useEffect(() => {
    onSearch();
  }, []);

  // parse SDob (string or Dayjs) into day, month name, year and compute age as on 15th June (current year)
  const getDobPartsAndAge = (sdob?: string | Dayjs) => {
    if (!sdob) return { day: '', month: '', year: '', ageAsOn15June: '' };
    let d =
      typeof sdob === 'string'
        ? dayjs(sdob, ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD', 'DD/MM/YY'], true)
        : dayjs(sdob);
    if (!d.isValid()) d = dayjs(sdob); // fallback to loose parse
    if (!d.isValid()) return { day: '', month: '', year: '', ageAsOn15June: '' };

    const day = d.format('DD');
    const month = d.format('MMMM'); // full month name
    const year = d.format('YYYY');

    // Compute age as on 15th June of current year (adjust year if you need admission-year logic)
    const asOf = dayjs(`${dayjs().year()}-06-15`);
    let age = asOf.diff(d, 'year');
    // If birthday after asOf in the same year, diff handles it; age is integer years
    if (age < 0) age = 0;

    return { day, month, year, ageAsOn15June: String(age) };
  };

  const dobParts = getDobPartsAndAge(studentData?.SDob);

  const printRef = useRef();

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Admission Form',
    pageStyle: `
      @page { size: A4; margin: 15mm; }
      body { -webkit-print-color-adjust: exact !important; }
    `,
  });

  return (
    <Box className="container">
      <Root>
        {/* PRINT BUTTON */}

        <Stack direction="row" justifyContent="end" alignItems="center" gap={2} p={2}>
          <TextField
            sx={{ width: 100 }}
            placeholder="Enter No"
            type="number"
            size="small"
            variant="outlined"
            value={searchNumber}
            onChange={handleSearchChange}
          />
          <Button variant="contained" color="primary" onClick={onSearch} disabled={searchNumber === '' || loading}>
            {loading ? 'Loading...' : 'Search'}
          </Button>
          <Box textAlign="right" my={2}>
            <Button
              variant="contained"
              color="success"
              className="print-button"
              onClick={handlePrint}
              startIcon={<PrintIcon />}
            >
              Print
            </Button>
          </Box>
        </Stack>
        {/* EVERYTHING BELOW PRINTS */}
        <Box ref={printRef}>
          {/* HEADER */}
          <Header>
            <Title>INFORMATION SHEET</Title>
            <Typography variant="subtitle2" fontSize={15}>
              No. 306
            </Typography>
          </Header>

          {/* CLASS FIELD */}
          <Typography fontSize={12} mb={2}>
            The class in which the admission is sought{' '}
            <Box display="inline-flex" width="300px">
              <UnderlineBox>{studentData?.ClassName}</UnderlineBox>
            </Box>{' '}
            class.
          </Typography>

          <Box>
            {/* 1. NAME OF THE PUPIL */}
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Typography fontSize={12}>1. Name of the Pupil</Typography>
                <Typography fontSize={12}>(in capital letters)</Typography>
              </Grid>

              <Grid item>:</Grid>

              <Grid item xs>
                <Grid container spacing={2}>
                  {[
                    { label: 'Surname', value: studentData?.StudentName },
                    { label: "Pupil's Name", value: '' },
                    { label: "Father's Name", value: studentData?.FathersName },
                    { label: "Mother's Name", value: studentData?.MotherName },
                  ].map((item) => (
                    <Grid item key={item.label} xs>
                      <UnderlineBox>{item.value}</UnderlineBox>
                      <Typography fontSize={12} align="center">
                        {item.label}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>

            {/* 2. DATE OF BIRTH */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>2. Date of Birth (in Figures)</Typography>
              </Grid>
              <Grid item>:</Grid>

              <Grid item xs>
                <Grid container spacing={2}>
                  {[
                    { label: 'Date', value: dobParts.day },
                    { label: 'Month', value: dobParts.month },
                    { label: 'Year', value: dobParts.year },
                    { label: 'Age as on 15/6', value: dobParts.ageAsOn15June },
                  ].map((item) => (
                    <Grid item key={item.label} xs>
                      <UnderlineBox>{item.value}</UnderlineBox>
                      <Typography align="center" fontSize={12}>
                        {item.label}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>

            {/* IN WORDS */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>(in words)</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SdobInWords}</UnderlineBox>
              </Grid>
            </Grid>

            {/* 3. PLACE OF BIRTH */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>3. Place of Birth</Typography>
              </Grid>
              <Grid item>:</Grid>

              <Grid item xs>
                <UnderlineBox>{studentData?.SPlaceOfBorth}</UnderlineBox>

                <Stack direction="row" justifyContent="space-between" mt={0}>
                  <Typography fontSize={12}>Place</Typography>
                  <Typography fontSize={12}>District</Typography>
                  <Typography fontSize={12}>State</Typography>
                </Stack>
              </Grid>
            </Grid>

            {/* 4. SEX */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>4. Sex</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <Typography fontSize={13} fontFamily="Poppins Semibold" mt={1}>
                  {studentData?.SGender === 0 ? 'Male' : 'Female'}
                </Typography>
              </Grid>
            </Grid>

            {/* 5. Mother Tongue */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>5. Mother Tongue</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SMotherTongue}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Nationality */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>6. Nationality</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SNationality}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Religion */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>7. Religion</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SReligion}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Caste */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>8. Caste</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SCaste}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Guardian Address */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>10. Father’s/Guardian’s Name & Address</Typography>
              </Grid>
              <Grid item>:</Grid>

              <Grid item xs>
                <UnderlineBox>{studentData?.SAddress1}</UnderlineBox>
                {/* <UnderlineBox mt={2}>Thoppil House, Kozhikode, Kerala</UnderlineBox> */}

                <Stack direction="row" spacing={2} mt={3}>
                  <Stack direction="row" spacing={1} flex={1}>
                    <Typography fontSize={12}>Tel.</Typography>
                    <UnderlineBox>{studentData?.FOfficeTelephone}</UnderlineBox>
                  </Stack>

                  <Stack direction="row" spacing={1} flex={1}>
                    <Typography fontSize={12}>Mob.</Typography>
                    <UnderlineBox>{studentData?.FOfficeMobile}</UnderlineBox>
                  </Stack>
                </Stack>
              </Grid>
            </Grid>

            {/* Mother's Name */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>11. Mother’s Name</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.MotherName}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Relationship */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>12. Relationship of Guardian to the Pupil</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.SchoolLastAttended}</UnderlineBox>
              </Grid>
            </Grid>

            {/* Occupation */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>13. Occupation of Father/Guardian</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>{studentData?.FOccupation}</UnderlineBox>
              </Grid>
            </Grid>

            {/* 13. Office Address */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>Office address</Typography>
              </Grid>

              <Grid item>:</Grid>

              <Grid item xs>
                {/* OFFICE ADDRESS MULTILINE UNDERLINE */}
                <Box
                  sx={{
                    // minHeight: '45px',
                    borderBottom: '1px solid #000',
                    width: '100%',
                    mb: 1,
                    display: 'flex',
                    alignItems: 'flex-end',
                    paddingBottom: '2px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    fontSize: '13px',
                    fontFamily: 'Poppins Semibold',
                  }}
                >
                  {/* ENTER LONG ADDRESS HERE */}
                  {/* Thoppil House, First Floor, Kozhikode, Kerala, India, Pin 673001 */}
                  {studentData?.FOfficeAddress}
                </Box>

                {/* PHONE LINE */}
                <Stack direction="row" spacing={2} mt={3}>
                  <Typography fontSize={12}>Phone</Typography>
                  <UnderlineBox>{studentData?.FOfficeMobile}</UnderlineBox>
                </Stack>
              </Grid>
            </Grid>

            {/* Educational Qualification */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>14. Educational Qualification of Parents</Typography>
              </Grid>
              <Grid item>:</Grid>

              <Grid item xs>
                <Stack direction="row" spacing={2}>
                  <Box flex={1}>
                    <UnderlineBox>{studentData?.FQualification}</UnderlineBox>
                    <Typography align="center" fontSize={12}>
                      Father
                    </Typography>
                  </Box>

                  <Box flex={1}>
                    <UnderlineBox>{studentData?.MQualification}</UnderlineBox>
                    <Typography align="center" fontSize={12}>
                      Mother
                    </Typography>
                  </Box>
                </Stack>
              </Grid>
            </Grid>

            {/* Annual Income */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>15. Annual Income</Typography>
              </Grid>
              <Grid item>:</Grid>

              <Grid item xs>
                <Stack direction="row" spacing={2}>
                  <Stack direction="row" flex={1} spacing={1}>
                    <Typography fontSize={12}>Rs.</Typography>
                    <UnderlineBox>{studentData?.FCompanyYear}</UnderlineBox>
                  </Stack>

                  <Stack direction="row" flex={1} spacing={1}>
                    <Typography fontSize={12}>Rs.</Typography>
                    <UnderlineBox>{studentData?.MCompanyYear}</UnderlineBox>
                  </Stack>
                </Stack>
              </Grid>
            </Grid>

            {/* Certificate Submitted */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={4}>
                <Typography fontSize={12}>16. Whether Birth Certificate/Leaving Certificate is submitted</Typography>
              </Grid>
              <Grid item>:</Grid>
              <Grid item xs>
                <UnderlineBox>Submitted</UnderlineBox>
              </Grid>
            </Grid>

            {/* Place */}
            <Grid container spacing={2} mt={3}>
              <Grid item xs={1}>
                <Typography fontSize={12}>Place</Typography>
              </Grid>
              <Grid item xs>
                <UnderlineBox width={200}>Kozhikode</UnderlineBox>
              </Grid>
            </Grid>

            {/* Date */}
            <Grid container spacing={2} mt={0}>
              <Grid item xs={1}>
                <Typography fontSize={12}>Date</Typography>
              </Grid>
              <Grid item xs>
                <UnderlineBox width={200}>17-11-2025</UnderlineBox>
              </Grid>
            </Grid>

            {/* Signature */}
            <Box mt={6} textAlign="right">
              <Typography fontSize={12}>Signature of Parent / Guardian</Typography>
            </Box>
          </Box>
        </Box>
        <Box textAlign="center" py={2}>
          <Button
            variant="contained"
            color="success"
            className="print-button"
            onClick={handlePrint}
            startIcon={<PrintIcon />}
          >
            Print
          </Button>
        </Box>
      </Root>
    </Box>
  );
}
