import React, { createContext, useContext, useState, ReactNode } from 'react';
import dayjs, { Dayjs } from 'dayjs';

export interface CalendarEvent {
  id: string;
  title: string;
  date: string; // 'YYYY-MM-DD'
  time?: string;
  description?: string;
}

interface CalendarContextType {
  selectedDate: Dayjs | null;
  setSelectedDate: (date: Dayjs | null) => void;
  events: CalendarEvent[];
  addEvent: (event: CalendarEvent) => void;
}

const CalendarContext = createContext<CalendarContextType | undefined>(undefined);

export const CalendarProvider = ({ children }: { children: ReactNode }) => {
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs());
  const [events, setEvents] = useState<CalendarEvent[]>([
    // 🎉 2025 (current & next months)
    { id: '1', title: 'Children’s Day', date: '2025-11-14', description: 'Celebrating childhood and students' },
    { id: '2', title: 'Sports Day', date: '2025-11-20', description: 'Annual inter-house sports competition' },
    { id: '3', title: 'Christmas Day', date: '2025-12-25', description: 'Christmas celebration' },
    { id: '4', title: 'New Year Celebration', date: '2025-12-31', description: 'Year-end celebration and party' },

    // 🎊 2026 (month by month)
    { id: '5', title: 'New Year’s Day', date: '2026-01-01', description: 'First day of the new year' },
    { id: '6', title: 'Republic Day', date: '2026-01-26', description: 'National holiday' },
    { id: '7', title: 'School Annual Day', date: '2026-01-30', description: 'Cultural performances and awards' },

    { id: '8', title: 'Science Exhibition', date: '2026-02-15', description: 'Students showcase science projects' },
    { id: '9', title: 'Mahashivratri', date: '2026-02-16', description: 'Hindu religious festival' },

    { id: '10', title: 'Holi', date: '2026-03-04', description: 'Festival of colors' },
    {
      id: '11',
      title: 'Parent-Teacher Meeting (Q1)',
      date: '2026-03-22',
      description: 'Quarterly parent-teacher meeting',
    },

    { id: '12', title: 'Good Friday', date: '2026-04-03', description: 'Christian holiday' },
    { id: '13', title: 'Ambedkar Jayanti', date: '2026-04-14', description: 'Dr. B.R. Ambedkar remembrance day' },

    { id: '14', title: 'Summer Vacation Begins', date: '2026-05-10', description: 'Start of summer holidays' },

    { id: '15', title: 'School Reopens', date: '2026-06-10', description: 'Classes resume after summer break' },
    {
      id: '16',
      title: 'Teacher Training Workshop',
      date: '2026-06-15',
      description: 'Professional development session',
    },

    { id: '17', title: 'Independence Day', date: '2026-08-15', description: 'National holiday celebration' },

    { id: '18', title: 'Onam Festival', date: '2026-08-27', description: 'Cultural celebration of Kerala' },

    { id: '19', title: 'Gandhi Jayanti', date: '2026-10-02', description: 'Birth anniversary of Mahatma Gandhi' },
    { id: '20', title: 'Mid-term Exams', date: '2026-10-10', description: 'Half-yearly academic examinations' },

    { id: '21', title: 'Diwali', date: '2026-11-08', description: 'Festival of lights' },
    { id: '22', title: 'Children’s Day', date: '2026-11-14', description: 'Celebrating students and fun activities' },

    { id: '23', title: 'Guru Nanak Jayanti', date: '2026-11-24', description: 'Sikh religious holiday' },

    { id: '24', title: 'Cultural Fest', date: '2026-12-12', description: 'Annual cultural event and performances' },
    { id: '25', title: 'Christmas Day', date: '2026-12-25', description: 'Christmas holiday' },
  ]);

  const addEvent = (event: CalendarEvent) => {
    setEvents((prev) => [...prev, event]);
  };

  return (
    <CalendarContext.Provider value={{ selectedDate, setSelectedDate, events, addEvent }}>
      {children}
    </CalendarContext.Provider>
  );
};

export const useCalendar = () => {
  const context = useContext(CalendarContext);
  if (!context) throw new Error('useCalendar must be used within CalendarProvider');
  return context;
};
