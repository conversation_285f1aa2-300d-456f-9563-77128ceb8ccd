import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stack,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  useTheme,
} from '@mui/material';
import { ArrowBackIosNew, ArrowForwardIos } from '@mui/icons-material';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, TimePicker } from '@mui/x-date-pickers';
import { useCalendar } from '@/contexts/CalenderContext';

// extend dayjs
dayjs.extend(isSameOrAfter);

const SchoolCalendarNew: React.FC = () => {
  const theme = useTheme();
  const today = dayjs();

  // ✅ Use global events from CalendarContext
  const { events, addEvent } = useCalendar();

  // Start calendar on the current month
  const [currentMonth, setCurrentMonth] = useState<Dayjs>(dayjs());
  const [open, setOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
  });

  // Calendar logic
  const monthStart = currentMonth.startOf('month');
  const startDay = monthStart.day(); // 0 (Sun) - 6 (Sat)
  const daysInMonth = currentMonth.daysInMonth();
  const prevMonth = currentMonth.subtract(1, 'month');
  const nextMonth = currentMonth.add(1, 'month');
  const daysInPrevMonth = prevMonth.daysInMonth();
  const totalCells = Math.ceil((daysInMonth + startDay) / 7) * 7;

  const daysArray = Array.from({ length: totalCells }, (_, i) => {
    const dayNumber = i - startDay + 1;
    let date: Dayjs;
    let inMonth = true;

    if (dayNumber <= 0) {
      date = prevMonth.date(daysInPrevMonth + dayNumber);
      inMonth = false;
    } else if (dayNumber > daysInMonth) {
      date = nextMonth.date(dayNumber - daysInMonth);
      inMonth = false;
    } else {
      date = currentMonth.date(dayNumber);
    }
    return { day: date.date(), date, inMonth };
  });

  // Add new event
  const handleAddEvent = () => {
    if (!newEvent.title.trim() || !selectedDate) return;

    addEvent({
      id: Date.now().toString(),
      title: newEvent.title,
      description: newEvent.description,
      date: dayjs(selectedDate).format('YYYY-MM-DD'),
      time: `${newEvent.startTime || '10:00'} - ${newEvent.endTime || '11:00'}`,
    });

    setOpen(false);
    setNewEvent({ title: '', description: '', startTime: '', endTime: '' });
  };

  const handleDayClick = (dateStr: string) => {
    setSelectedDate(dateStr);
    setOpen(true);
  };

  // ✅ Filter upcoming events (today + future)
  const filteredUpcoming = [...events]
    .filter((e) => dayjs(e.date).isSameOrAfter(today, 'day'))
    .sort((a, b) => dayjs(a.date).unix() - dayjs(b.date).unix());

  return (
    <Stack direction="row" spacing={2}>
      {/* Left Side — Upcoming Events */}
      <Stack direction="column" flex={1} pl={2} pt={2}>
        <Stack>
          <Typography variant="h5" sx={{ fontFamily: 'Poppins Semibold', mb: 0.5 }}>
            Upcoming Events
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Don’t miss schedule
          </Typography>
        </Stack>
        <Box sx={{ height: 'calc(100vh - 227px)', overflow: 'auto', '&::-webkit-scrollbar': { display: 'none' } }} pb={2}>
          <Stack spacing={2}>
            {filteredUpcoming.map((event) => (
              <Paper
                key={event.id}
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  backgroundColor: '#fff',
                  boxShadow: '0px 2px 8px rgba(0,0,0,0.05)',
                }}
              >
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
                  <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: theme.palette.primary.main }} />
                  <Typography variant="body2" color="text.secondary">
                    {dayjs(event.date).format('MMM DD, YYYY')} - {event.time}
                  </Typography>
                </Stack>
                <Typography variant="subtitle1" sx={{ fontFamily: 'Poppins Medium', mb: 0.5 }}>
                  {event.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {event.description}
                </Typography>
              </Paper>
            ))}
          </Stack>
        </Box>
      </Stack>

      {/* Right Side — Calendar */}
      <Box flex={1.5} pt={3} pr={3} pb={2.5} sx={{ overflow: 'auto', height: 'calc(100vh - 128px)', '&::-webkit-scrollbar': { display: 'none' } }}>
        <Paper elevation={0} sx={{ p: 3, borderRadius: 2, bgcolor: '#fff', boxShadow: '0px 2px 10px rgba(0,0,0,0.05)' }}>
          {/* Header */}
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Typography variant="h6" fontSize={17} fontWeight={600}>
              {currentMonth.format('MMMM YYYY')}
            </Typography>
            <Stack direction="row" alignItems="center" spacing={1}>
              <IconButton onClick={() => setCurrentMonth(currentMonth.subtract(1, 'month'))}>
                <ArrowBackIosNew fontSize="small" />
              </IconButton>
              <IconButton onClick={() => setCurrentMonth(currentMonth.add(1, 'month'))}>
                <ArrowForwardIos fontSize="small" />
              </IconButton>
            </Stack>
          </Stack>

          {/* Weekdays */}
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', textAlign: 'center', fontWeight: 600, fontSize: '0.9rem', mb: 1 }}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((d) => (
              <Box key={d}>{d}</Box>
            ))}
          </Box>

          {/* Calendar Grid */}
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', border: '1px solid #e0e7ff', borderRadius: 1, overflow: 'hidden' }}>
            {daysArray.map(({ day, date, inMonth }, i) => {
              const formatted = date.format('YYYY-MM-DD');
              const dayEvents = events.filter((e) => e.date === formatted);

              return (
                <Box
                  key={i}
                  onClick={() => handleDayClick(formatted)}
                  sx={{
                    height: 100,
                    p: 1,
                    border: '1px solid #e0e7ff',
                    bgcolor: inMonth ? theme.palette.common.white : theme.palette.grey[100],
                    opacity: inMonth ? 1 : 0.55,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: '#eef2ff' },
                  }}
                >
                  <Typography variant="body2" fontWeight={600} sx={{ mb: 0.5, fontSize: '0.8rem', color: inMonth ? '#111' : '#666' }}>
                    {day}
                  </Typography>

                  {dayEvents.map((e) => (
                    <Box
                      key={e.id}
                      sx={{
                        bgcolor: theme.palette.primary.light + '33',
                        borderLeft: `3px solid ${theme.palette.primary.main}`,
                        borderRadius: 1,
                        p: 0.5,
                        fontSize: '0.7rem',
                        mb: 0.5,
                      }}
                    >
                      <Typography variant="body2" sx={{ fontWeight: 600, fontSize: '0.75rem', color: '#111827' }}>
                        {e.title}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#555', fontSize: '0.65rem' }}>
                        {e.time}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              );
            })}
          </Box>
        </Paper>
      </Box>

      {/* Add Event Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} fullWidth maxWidth="sm">
        <DialogTitle>Add New Event</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 1 }}>
            Date: {selectedDate ? dayjs(selectedDate).format('DD MMM YYYY') : '—'}
          </Typography>
          <Stack spacing={2}>
            <TextField
              placeholder="Title"
              value={newEvent.title}
              onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
              fullWidth
            />
            <TextField
              placeholder="Description"
              value={newEvent.description}
              onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
              fullWidth
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <Stack direction="row" spacing={2}>
                <TimePicker
                  label="Start Time"
                  value={newEvent.startTime ? dayjs(newEvent.startTime, 'HH:mm') : null}
                  onChange={(value) => setNewEvent({ ...newEvent, startTime: value ? dayjs(value).format('HH:mm') : '' })}
                  slotProps={{ textField: { fullWidth: true } }}
                />
                <TimePicker
                  label="End Time"
                  value={newEvent.endTime ? dayjs(newEvent.endTime, 'HH:mm') : null}
                  onChange={(value) => setNewEvent({ ...newEvent, endTime: value ? dayjs(value).format('HH:mm') : '' })}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Stack>
            </LocalizationProvider>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddEvent}>
            Add Event
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
};

export default SchoolCalendarNew;
