import React from 'react';
import { <PERSON>, CardContent, CardMedia, Typography, Stack, Box, Button, useTheme } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import styled from 'styled-components';

const EventCardRoot = styled(Card)`
  width: 320px;
  border-radius: 20px !important;
  background-color: ${({ theme }) => (theme.palette.mode === 'dark' ? '#1c1c1c' : theme.palette.grey[100])};
  color: ${({ theme }) => theme.palette.text.primary};
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.35);
  }
`;

const Jo<PERSON><PERSON><PERSON><PERSON> = styled(But<PERSON>)`
  border-radius: 12px !important;
  text-transform: none !important;
  padding: 4px 16px !important;
  font-weight: 500 !important;
  background-color: ${({ theme }) => theme.palette.primary.main} !important;
  color: #fff !important;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${({ theme }) => theme.palette.primary.dark} !important;
    transform: scale(1.05);
  }
`;

const EventCard: React.FC = () => {
  const theme = useTheme();

  return (
    <EventCardRoot theme={theme}>
      <CardMedia
        component="img"
        height="200"
        image="/beb3bcff-6e9e-44d3-bf15-3a103a2aef44.jpeg"
        alt="Event Banner"
        sx={{
          objectFit: 'cover',
          borderTopLeftRadius: '20px',
          borderTopRightRadius: '20px',
        }}
      />

      <CardContent>
        <Stack spacing={1.2}>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              fontSize: 16,
              lineHeight: 1.3,
            }}
          >
            UI/UX Design Workshop 2025
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
            Learn the fundamentals of user experience design and usability from industry experts.
          </Typography>

          <Stack spacing={1} sx={{ mt: 1 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <CalendarMonthIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption">Nov 20, 2025</Typography>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={1}>
              <LocationOnIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption">Bangalore, India</Typography>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={1}>
              <PeopleAltIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption">120 Attendees</Typography>
            </Stack>
          </Stack>

          <Stack direction="row" justifyContent="flex-end" alignItems="center" sx={{ mt: 2 }}>
            <JoinButton theme={theme}>Join Event</JoinButton>
          </Stack>
        </Stack>
      </CardContent>
    </EventCardRoot>
  );
};

export default EventCard;
