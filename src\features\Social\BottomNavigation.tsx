import * as React from 'react';
import Box from '@mui/material/Box';
import CssBaseline from '@mui/material/CssBaseline';
import BottomNavigation from '@mui/material/BottomNavigation';
import BottomNavigationAction from '@mui/material/BottomNavigationAction';
import Paper from '@mui/material/Paper';
import { Avatar, useTheme } from '@mui/material';
import {
  AddFilledIcon,
  AddOutlinedIcon,
  HomeFilledIcon,
  HomeOutlinedIcon,
  PlayFilledIcon,
  PlayOutlinedIcon,
  SearchFilledIcon,
  SearchOutlinedIcon,
} from '@/theme/overrides/CustomIcons';
import { currentUser, getInitials } from './Social';

export default function FixedBottomNavigation({
  user,
  createPost,
  setIsDrawerOpen,
  isDrawerOpen,
  isprofileReelOpen,
  openCreatePostDialog,
  onOpenReel,
  allPosts,
  handleOpenProfile,
  setOpenMyPost,
  tabInitialValue,
  setTabInitialValue,
  otherUsers,
  setOpenSearch,
  openSearch,
  selectedProfileUser,
  openComments,
  reelViewerState,
}: any) {
  const theme = useTheme();
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    // if (isDrawerOpen) {
    //   setTabInitialValue(4);
    // }
    // if (selectedProfileUser.userName !== user.userName) {
    //   setTabInitialValue(1);
    // }
    console.log('tabInitialValue::::----', tabInitialValue);
  }, [tabInitialValue, selectedProfileUser, setTabInitialValue, user]);

  return (
    <Box sx={{ pb: 9.5 }} ref={ref} zIndex={11111}>
      <CssBaseline />
      <Paper
        sx={{
          pb: 2,
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex:
            isDrawerOpen &&
            !isprofileReelOpen &&
            !openSearch &&
            !openComments &&
            !reelViewerState &&
            !openCreatePostDialog
              ? 111111
              : 1,
        }}
        elevation={3}
      >
        <BottomNavigation
          // Remove default padding
          sx={{ px: 0 }}
          showLabels={false}
          value={tabInitialValue}
          onChange={(event, newValue) => {
            setTabInitialValue(newValue);
          }}
        >
          {/* 🏠 Home */}
          <BottomNavigationAction
            disableRipple
            icon={tabInitialValue === 0 ? <HomeFilledIcon /> : <HomeOutlinedIcon />}
            onClick={() => {
              setTabInitialValue(0);
              setIsDrawerOpen(false);
            }}
            sx={{
              minWidth: 0,
              p: 0,
              mx: 0,
              color: 'black',
              '.MuiBottomNavigationAction-label': { display: 'none' },
              '&.Mui-selected': {
                color: 'black', // 👈 ensure selected state stays black
              },
            }}
          />
          <BottomNavigationAction
            disableRipple
            icon={tabInitialValue === 1 ? <SearchFilledIcon /> : <SearchOutlinedIcon />}
            onClick={() => {
              setTabInitialValue(1);
              setOpenSearch(true);
            }}
            sx={{
              minWidth: 0,
              p: 0,
              mx: 0,
              color: 'black',
              '.MuiBottomNavigationAction-label': { display: 'none' },
              '&.Mui-selected': {
                color: 'black', // 👈 ensure selected state stays black
              },
            }}
          />
          <BottomNavigationAction
            disableRipple
            onClick={createPost}
            icon={tabInitialValue === 2 ? <AddFilledIcon /> : <AddOutlinedIcon />}
            sx={{
              minWidth: 0,
              p: 0,
              mx: 0,
              color: 'black',
              '.MuiBottomNavigationAction-label': { display: 'none' },
              '&.Mui-selected': {
                color: 'black', // 👈 ensure selected state stays black
              },
            }}
          />
          <BottomNavigationAction
            disableRipple
            onClick={() => onOpenReel(allPosts[0]?.id, 0, true)}
            icon={
              tabInitialValue === 3 ? (
                <PlayFilledIcon sx={{ fontSize: '28px' }} />
              ) : (
                <PlayOutlinedIcon sx={{ fontSize: '28px' }} />
              )
            }
            sx={{
              minWidth: 0,
              p: 0,
              mx: 0,
              color: 'black',
              '.MuiBottomNavigationAction-label': { display: 'none' },
              '&.Mui-selected': {
                color: 'black', // 👈 ensure selected state stays black
              },
            }}
          />
          <BottomNavigationAction
            disableRipple
            onClick={() => {
              setIsDrawerOpen(true);
              setOpenMyPost(false);
              if (user.userName) {
                handleOpenProfile(user.userName);
              }
            }}
            icon={
              <Avatar
                src={user.avatar}
                sx={{
                  border: 2,
                  borderColor: tabInitialValue === 4 ? 'black' : '',
                  width: 30,
                  height: 30,
                  p: 0, // Remove Avatar padding
                  m: 0,
                  cursor: 'pointer',
                  bgcolor: theme.palette.secondary.main,
                  fontSize: '2rem',
                  // border: `3px solid ${theme.palette.primary.main}`,
                  color: theme.palette.common.white,
                }}
              >
                {getInitials(user.userName)}
              </Avatar>
            }
            sx={{
              minWidth: 0,
              p: 0,
              mx: 0,
              '.MuiBottomNavigationAction-label': { display: 'none' },
            }}
          />
        </BottomNavigation>
      </Paper>
    </Box>
  );
}
