import api from '@/api';
import { SendResponse } from '@/types/Common';
import {
  LiveClassAttendReportData,
  LiveClassListData,
  LiveClassListRequest,
  LiveClassUnAttendReportData,
} from '@/types/LiveClass';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchLiveClassList = createAsyncThunk<LiveClassListData[], LiveClassListRequest, { rejectValue: string }>(
  'liveClass/liveClassList',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.LiveClass.GetLiveClassList(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in fetching Live Class List');
    }
  }
);

export const endZoomLiveClass = createAsyncThunk<
  SendResponse,
  { adminId: number; liveclassId: number },
  { rejectValue: string }
>('liveClass/endZoomLiveClass', async ({ adminId, liveclassId }, { rejectWithValue }) => {
  try {
    const response = await api.LiveClass.EndZoomLiveClass(adminId, liveclassId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in ending Zoom Live Class');
  }
});

export const deleteZoomLiveClass = createAsyncThunk<
  SendResponse,
  { adminId: number; liveclassId: number },
  { rejectValue: string }
>('liveClass/deleteZoomLiveClass', async ({ adminId, liveclassId }, { rejectWithValue }) => {
  try {
    const response = await api.LiveClass.DeleteZoomLiveClass(adminId, liveclassId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in deleting Zoom Live Class');
  }
});

export const fetchLiveClassAttendReport = createAsyncThunk<
  LiveClassAttendReportData[],
  { adminId: number; liveclassId: number },
  { rejectValue: string }
>('liveClass/liveClassAttendReport', async ({ adminId, liveclassId }, { rejectWithValue }) => {
  try {
    const response = await api.LiveClass.GetLiveClassAttendReport(adminId, liveclassId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Live Class Attend Report');
  }
});

export const fetchLiveClassUnAttendReport = createAsyncThunk<
  LiveClassUnAttendReportData[],
  { adminId: number; academicId: number; classId: number; liveclassId: number },
  { rejectValue: string }
>('liveClass/liveClassUnAttendReport', async ({ adminId, academicId, classId, liveclassId }, { rejectWithValue }) => {
  try {
    const response = await api.LiveClass.GetLiveClassUnAttendReport(adminId, academicId, classId, liveclassId);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Live Class UnAttend Report');
  }
});
