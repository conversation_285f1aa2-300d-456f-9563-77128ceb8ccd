import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Divider,
  TextField,
  Button,
  Tooltip,
  useTheme,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import VideocamIcon from '@mui/icons-material/Videocam';
import SubjectIcon from '@mui/icons-material/Subject';
import { RiShareForwardLine } from 'react-icons/ri';
import { currentUser } from './Social';
import { Snackbar } from '@mui/material';
import { Alert } from '@mui/material';
import { SendOutlinedIcon } from '@/theme/overrides/CustomIcons';

interface Story {
  id: number;
  userName: string;
  avatar: string;
  media?: string | null;
  mediaType: 'image' | 'video' | 'text' | null;
  text?: string;
  timestamp: number;
  duration: number;
}

interface CreateStoryDrawerProps {
  open: boolean;
  onClose: () => void;
  onStoryCreate: (story: Story) => void;
}

const CreateStoryDrawer: React.FC<CreateStoryDrawerProps> = ({ open, onClose, onStoryCreate }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<'Image' | 'Video' | 'Text'>('Text');
  const [storyText, setStoryText] = useState('');
  const [storyMedia, setStoryMedia] = useState<string | null>(null);
  const [mediaType, setMediaType] = useState<'image' | 'video' | 'text' | null>(null);

  // Snackbar states
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'error' | 'info' | 'success' | 'warning'>('info');

  const showSnackbar = (message: string, severity: 'error' | 'info' | 'success' | 'warning' = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = (_?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') return;
    setSnackbarOpen(false);
  };

  useEffect(() => {
    if (open) {
      setStoryText('');
      setStoryMedia(null);
      setMediaType('text');
      setActiveTab('Text');
    }
  }, [open]);

  // const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'image' | 'video') => {
  //   const file = e.target.files?.[0];
  //   if (file) {
  //     const mediaUrl = URL.createObjectURL(file);
  //     setStoryMedia(mediaUrl);
  //     setMediaType(type);
  //     setActiveTab(type === 'image' ? 'Image' : 'Video');
  //   }
  // };

  // Handle media change (image/video upload)
  const handleMediaChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: 'image' | 'video',
    activeTabShow: 'Image' | 'Video' | 'Text'
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setActiveTab(activeTabShow);
    const mediaUrl = URL.createObjectURL(file);
    setStoryMedia(mediaUrl);
    setMediaType(type);

    // 🎥 Video validation
    if (type === 'video') {
      const videoElement = document.createElement('video');
      videoElement.src = mediaUrl;

      videoElement.onloadedmetadata = () => {
        if (videoElement.duration > 60) {
          showSnackbar('Please select a video shorter than 60 seconds.', 'error');
          setStoryMedia(null);
          setMediaType(null);
        }
      };
    }

    // 🖼️ Image validation
    if (type === 'image') {
      if (file.size > 5 * 1024 * 1024) {
        showSnackbar('Please select an image smaller than 5MB.', 'error');
        setStoryMedia(null);
        setMediaType(null);
      }
    }
  };

  const handleRemoveMedia = () => {
    setStoryMedia(null);
    setMediaType(null);
    showSnackbar('Media removed successfully.', 'info');
  };

  // const handleRemoveMedia = () => {
  //   setStoryMedia(null);
  //   setMediaType('text');
  // };

  const handleCreate = () => {
    if (!storyText.trim() && !storyMedia) return;

    const newStory: Story = {
      id: Date.now(),
      userName: currentUser.userName,
      avatar: currentUser.avatar,
      media: storyMedia,
      mediaType,
      text: storyText,
      timestamp: Date.now(),
      duration: 5,
    };
    onStoryCreate(newStory);
    onClose();
  };

  const isContentReady = storyText.trim() || storyMedia;

  const handleTabChange = (_: React.MouseEvent<HTMLElement>, newValue: 'Image' | 'Video' | 'Text' | null) => {
    if (!newValue) return;
    if (storyMedia && newValue !== activeTab) {
      showSnackbar('Please remove current media to switch type.', 'warning');
    } else {
      setActiveTab(newValue);
    }
  };

  const tabContent = useMemo(() => {
    // ✅ Show preview if media selected
    if (storyMedia) {
      return (
        <Box>
          {mediaType === 'image' && (
            <Box mt={2} sx={{ position: 'relative' }}>
              <img
                src={storyMedia}
                alt="preview"
                style={{
                  width: '100%',
                  borderRadius: 12,
                  maxHeight: '300px',
                  objectFit: 'cover',
                }}
              />
              <Tooltip title="Remove image">
                <IconButton
                  onClick={handleRemoveMedia}
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    bgcolor: 'rgba(0, 0, 0, 0.5)',
                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' },
                  }}
                >
                  <CloseIcon sx={{ color: 'white' }} />
                </IconButton>
              </Tooltip>
            </Box>
          )}

          {mediaType === 'video' && (
            <video
              src={storyMedia}
              controls
              preload="metadata"
              style={{
                width: '100%',
                maxHeight: '300px',
                objectFit: 'cover',
                borderRadius: 12,
                marginTop: 10,
              }}
            />
          )}

          <TextField
            fullWidth
            multiline
            placeholder="Add a caption..."
            value={storyText}
            onChange={(e) => setStoryText(e.target.value)}
            sx={{ mt: 2, '& .MuiOutlinedInput-root': { borderRadius: '12px' } }}
          />
        </Box>
      );
    }

    // ✅ No media selected → show tab UI
    switch (activeTab) {
      case 'Image':
        return (
          <Box sx={{ textAlign: 'center', py: 10 }}>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              No photo selected.
            </Typography>
            <Button component="label" variant="contained" startIcon={<PhotoCameraIcon />} size="large">
              Upload Photo
              <input hidden accept="image/*" type="file" onChange={(e) => handleMediaChange(e, 'image', 'Image')} />
            </Button>
          </Box>
        );

      case 'Video':
        return (
          <Box sx={{ textAlign: 'center', py: 10 }}>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              No video selected.
            </Typography>
            <Button component="label" variant="contained" startIcon={<VideocamIcon />} size="large">
              Upload Video
              <input hidden accept="video/*" type="file" onChange={(e) => handleMediaChange(e, 'video', 'Video')} />
            </Button>
          </Box>
        );

      case 'Text':
      default:
        return (
          <TextField
            fullWidth
            multiline
            placeholder="Write your story..."
            value={storyText}
            onChange={(e) => setStoryText(e.target.value)}
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '12px' }, mt: 2 }}
            // minRows={storyMedia ? 2 : 5}
            // maxRows={storyMedia ? 4 : 8}
            // InputProps={{
            //   inputProps: {
            //     style: { resize: 'vertical', width: '100%', minHeight: '2.4375em', maxHeight: '100px' },
            //   },
            // }}
          />
        );
    }
  }, [activeTab, storyMedia, storyText, mediaType, handleMediaChange, handleRemoveMedia]);

  // const handleTabChange = (_: any, newValue: 'Image' | 'Video' | 'Text') => {
  //   if (!storyMedia) {
  //     setActiveTab(newValue);
  //     setMediaType(newValue.toLowerCase() as 'image' | 'video' | 'text');
  //   } else if (newValue !== activeTab) {
  //     alert('Please remove the current media to switch.');
  //   }
  // };

  return (
    <Drawer
      anchor="bottom"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          height: '100vh',
          backgroundColor: theme.palette.background.paper,
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          px: 2,
          py: 2,
          position: 'relative',
        }}
      >
        <IconButton onClick={onClose} sx={{ position: 'absolute', right: 2, top: 2 }}>
          <CloseIcon />
        </IconButton>
        <Typography textAlign="center" variant="subtitle2" fontFamily="Poppins Semibold">
          Create Story
        </Typography>
      </Box>

      <Divider />

      {/* Scrollable Content */}
      <Box sx={{ p: 2, pb: 2, overflowY: 'auto', maxHeight: 'calc(100vh - 180px)' }}>{tabContent}</Box>

      {/* Post Button */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 90,
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'center',
          zIndex: 11,
        }}
      >
        <Button
          variant="contained"
          onClick={handleCreate}
          disabled={!isContentReady}
          endIcon={<RiShareForwardLine />}
          sx={{
            borderRadius: '999px',
            px: 4,
            py: 1.2,
            fontWeight: 600,
            textTransform: 'none',
            boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
          }}
        >
          Share
        </Button>
      </Box>

      <Box
        sx={{
          position: 'fixed',
          bottom: 16,
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'center',
          zIndex: 12,
          backdropFilter: 'blur(10px)',
        }}
      >
        <ToggleButtonGroup
          exclusive
          fullWidth
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            border: 0,
            display: 'flex',
            gap: 1,
            bgcolor: 'rgba(255,255,255,0.8)',
            borderRadius: 10,
            p: 0,
            mx: 2,
            boxShadow: 4,
            '& .MuiToggleButtonGroup-grouped': {
              border: 'none',
              borderRadius: '50px !important', // ✅ ensures corners apply
            },
          }}
        >
          <ToggleButton
            value="Text"
            size="small"
            disableRipple
            disableFocusRipple
            disableTouchRipple
            sx={{
              px: 3,
              borderRadius: 0,
              textTransform: 'none',
              fontFamily: activeTab === 'Text' ? 'Poppins bold' : 'Poppins Semibold',
              fontSize: 15,
              color: activeTab === 'Text' ? 'white' : 'black',
              bgcolor: activeTab === 'Text' ? theme.palette.primary.main : 'transparent',
              '&:hover': { bgcolor: activeTab === 'Text' ? theme.palette.primary.dark : '#f0f0f0' },
            }}
          >
            Text
          </ToggleButton>

          <ToggleButton
            value="Image"
            size="small"
            component="label"
            disableRipple
            disableFocusRipple
            disableTouchRipple
            sx={{
              px: 3,
              borderRadius: 8,
              textTransform: 'none',
              fontFamily: activeTab === 'Image' ? 'Poppins bold' : 'Poppins Semibold',
              fontSize: 15,
              color: activeTab === 'Image' ? 'white' : 'black',
              bgcolor: activeTab === 'Image' ? theme.palette.primary.main : 'transparent',
              '&:hover': { bgcolor: activeTab === 'Image' ? theme.palette.primary.dark : '#f0f0f0' },
            }}
          >
            Image
          </ToggleButton>

          <ToggleButton
            value="Video"
            size="small"
            component="label"
            disableRipple
            disableFocusRipple
            disableTouchRipple
            sx={{
              px: 3,
              borderRadius: 8,
              textTransform: 'none',
              fontFamily: activeTab === 'Video' ? 'Poppins bold' : 'Poppins Semibold',
              fontSize: 15,
              color: activeTab === 'Video' ? 'white' : 'black',
              bgcolor: activeTab === 'Video' ? theme.palette.primary.main : 'transparent',
              '&:hover': { bgcolor: activeTab === 'Video' ? theme.palette.primary.dark : '#f0f0f0' },
            }}
          >
            Video
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={snackbarSeverity} variant="standard" sx={{ width: '100%' }} onClose={handleSnackbarClose}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Drawer>
  );
};

export default CreateStoryDrawer;
