import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import { AssignmentDataType, AssignmentRequest } from '@/types/Assignment';

async function GetAssignmentList(request: AssignmentRequest): Promise<APIResponse<AssignmentDataType[]>> {
  const response = await privateApi.post<AssignmentDataType[]>(ApiUrls.GetAssignmentList, request);
  console.log('response', response);
  return response;
}

const methods = {
  GetAssignmentList,
};

export default methods;
