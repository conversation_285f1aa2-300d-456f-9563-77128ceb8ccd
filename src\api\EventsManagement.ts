import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import {
  CreateEventsRequestType,
  EventsDetailsListFilters,
  EventsDetailsListType,
  UploadEventsResponse,
} from '@/types/AcademicManagement';
import { SendResponse } from '@/types/Common';

async function UploadEventsFile(file: File): Promise<APIResponse<UploadEventsResponse>> {
  try {
    const formData = new FormData();
    formData.append('files', file);

    const response = await privateApi.post<UploadEventsResponse>(ApiUrls.EventUpload, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  } catch (error) {
    console.error('File upload error:', error);
    throw new Error('Failed to upload files');
  }
}

async function CreateEvents(request: CreateEventsRequestType): Promise<APIResponse<SendResponse>> {
  const response = await privateApi.post<SendResponse>(ApiUrls.CreateEvent, request);
  return response;
}

async function GetEventsDetailsList(request: EventsDetailsListFilters): Promise<APIResponse<EventsDetailsListType[]>> {
  const response = await privateApi.post<EventsDetailsListType[]>(ApiUrls.GetEventsDetailsList, request);
  return response;
}

const methods = {
  UploadEventsFile,
  CreateEvents,
  GetEventsDetailsList,
};

export default methods;
