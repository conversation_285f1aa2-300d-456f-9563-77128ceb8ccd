import { FetchStatus } from './Common';

// Online Exam //

// Objective Exam //
export type ObjectiveExamRequest = {
  adminId: number;
  academicId: number | undefined;
  classId: number;
  subjectId: number;
  createdDate: string | null;
  status: number;
};

export type ObjectiveExamDataType = {
  oExamId: number;
  oExamKey: string;
  adminId: number;
  accademicId: number | undefined;
  classId: number;
  subjectId: number;
  name: string;
  description: string;
  duration: number;
  startTime: number;
  endTime: number;
  createdDate: number | null;
  modifiedDate: number;
  allowedPause: number;
  status: number;
  className: string;
  subjectName: string;
  accademicTime: string;
  createdBy: string;
  totalQuestion: number;
  subjectIcon: string;
};

// Descriptive Exam //
export type DescriptiveExamRequest = {
  adminId: number;
  academicId: number | undefined;
  classId: number;
  subjectId: number;
  createdDate: string | null;
  status: number;
};

export type DescriptiveExamDataType = {
  examId: number;
  examKey: string;
  adminId: number;
  accademicId: number | undefined;
  classId: number;
  subjectId: number;
  name: string;
  description: string;
  duration: number;
  startTime: number;
  endTime: number;
  createdDate: number | null;
  modifiedDate: number;
  allowedPause: number;
  status: number;
  className: string;
  subjectName: string;
  accademicTime: string;
  createdBy: string;
  totalQuestion: number;
  subjectIcon: string;
};

// Online Exam State //
export type OnlineExamState = {
  objectiveExamList: {
    status: FetchStatus;
    data: ObjectiveExamDataType[];
    error: string | null;
  };
  descriptiveExamList: {
    status: FetchStatus;
    data: DescriptiveExamDataType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
