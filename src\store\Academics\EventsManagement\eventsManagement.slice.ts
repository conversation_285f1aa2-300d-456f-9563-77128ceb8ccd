import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import type { EventsManagementState } from '@/types/AcademicManagement';
import { flushStore } from '@/store/flush.slice';
import { createEvents, fetchEventsDetailsList, uploadEventsFile } from './eventsManagement.thunks';

const initialState: EventsManagementState = {
  eventsDetailsList: {
    status: 'idle',
    data: [],
    error: null,
    pageInfo: {
      totalrecords: 0,
      pagesize: 20,
      pagenumber: 1,
      totalpages: 0,
      remainingpages: 0,
    },
    sortColumn: 'eventTitle',
    sortDirection: 'asc',
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

const eventsManagementSlice = createSlice({
  name: 'eventsManagement',
  initialState,
  reducers: {
    setEventsDetailsListPage: (state, action: PayloadAction<number>) => {
      state.eventsDetailsList.pageInfo.pagenumber = action.payload;
    },
    setEventsDetailsListPageSize: (state, action: PayloadAction<number>) => {
      state.eventsDetailsList.pageInfo.pagesize = action.payload;
    },
    setSortColumn: (state, action: PayloadAction<string>) => {
      state.eventsDetailsList.sortColumn = action.payload;
    },
    setSortDirection: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.eventsDetailsList.sortDirection = action.payload;
    },
    setSortColumnAndDirection: (state, action: PayloadAction<{ column: string; direction: 'asc' | 'desc' }>) => {
      state.eventsDetailsList.pageInfo.pagenumber = 1;
      state.eventsDetailsList.sortColumn = action.payload.column;
      state.eventsDetailsList.sortDirection = action.payload.direction;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    setDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      state.deletingRecords[action.payload.recordId] = true;
    },
    clearDeletingRecords: (state, action: PayloadAction<{ recordId: number }>) => {
      delete state.deletingRecords[action.payload.recordId];
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching Events Details List
      // ------Get------- //
      .addCase(fetchEventsDetailsList.pending, (state) => {
        state.eventsDetailsList.status = 'loading';
        state.eventsDetailsList.error = null;
      })
      .addCase(fetchEventsDetailsList.fulfilled, (state, action) => {
        state.eventsDetailsList.status = 'success';
        state.eventsDetailsList.data = action.payload;
        state.eventsDetailsList.error = null;
      })
      .addCase(fetchEventsDetailsList.rejected, (state, action) => {
        state.eventsDetailsList.status = 'error';
        state.eventsDetailsList.error = action.payload || 'Unknown error in fetching Events Details List';
      })
      // ------Create------- //
      .addCase(createEvents.pending, (state) => {
        state.submitting = true;
        state.eventsDetailsList.error = null;
      })
      .addCase(createEvents.fulfilled, (state) => {
        state.submitting = false;
        state.eventsDetailsList.error = null;
      })
      .addCase(createEvents.rejected, (state, action) => {
        state.submitting = false;
        state.eventsDetailsList.error = action.error.message || 'Unknown error in creating Events';
      })
      // ------File Upload------- //
      .addCase(uploadEventsFile.pending, (state) => {
        state.submitting = true;
        state.eventsDetailsList.error = null;
      })
      .addCase(uploadEventsFile.fulfilled, (state) => {
        state.submitting = false;
        state.eventsDetailsList.error = null;
      })
      .addCase(uploadEventsFile.rejected, (state, action) => {
        state.submitting = false;
        state.eventsDetailsList.error = action.error.message || 'Unknown error in fetching Events File Upload';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const {
  setEventsDetailsListPage,
  setEventsDetailsListPageSize,
  setSortColumn,
  setSortDirection,
  setSortColumnAndDirection,
  setSubmitting,
  setDeletingRecords,
  clearDeletingRecords,
} = eventsManagementSlice.actions;

export default eventsManagementSlice.reducer;
