import api from '@/api';
import {
  EventsDetailsListType,
  CreateEventsRequestType,
  UploadEventsResponse,
  EventsDetailsListFilters,
} from '@/types/AcademicManagement';
import { SendResponse } from '@/types/Common';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchEventsDetailsList = createAsyncThunk<
  EventsDetailsListType[],
  EventsDetailsListFilters,
  { rejectValue: string }
>('eventsManagement/eventsDetailsList', async (request, { rejectWithValue }) => {
  try {
    const response = await api.EventsManagement.GetEventsDetailsList(request);
    return response.data;
  } catch {
    return rejectWithValue('Something went wrong in fetching Events Details List');
  }
});

export const createEvents = createAsyncThunk<SendResponse, CreateEventsRequestType, { rejectValue: string }>(
  'eventsManagement/createEvents',
  async (request, { rejectWithValue }) => {
    try {
      const response = await api.EventsManagement.CreateEvents(request);
      return response.data;
    } catch {
      return rejectWithValue('Something went wrong in creating Events');
    }
  }
);

export const uploadEventsFile = createAsyncThunk<UploadEventsResponse, { files: File }, { rejectValue: string }>(
  'eventsManagement/FileUpload',
  async ({ files }, { rejectWithValue }) => {
    try {
      const response = await api.EventsManagement.UploadEventsFile(files);
      // response is APIResponse<UploadEventsResponse>, extract the data property
      return response.data;
    } catch (error) {
      console.error('Upload file error:', error);
      return rejectWithValue('Something went wrong in Upload files');
    }
  }
);
