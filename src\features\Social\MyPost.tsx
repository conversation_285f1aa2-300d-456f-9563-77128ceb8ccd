import React, { useState, useCallback } from 'react';

// --- <PERSON><PERSON><PERSON> UTILITIES AND DATA ---

// Mocking theme colors for Tailwind integration
const theme = {
  themeMode: 'light',
  palette: {
    primary: { main: '#4F46E5' }, // indigo-600
    success: { main: '#10B981', lighter: '#D1FAE5' }, // emerald-500, emerald-100
    grey: { 200: '#E5E7EB', 300: '#D1D5DB' }, // gray-200, gray-300
    common: { white: '#FFFFFF' },
  },
};

const user = {
  id: 'u1',
  displayName: '<PERSON>',
  photo: 'https://placehold.co/40x40/4F46E5/ffffff?text=JD',
  email: '<EMAIL>',
};

const getInitials = (user) => {
  if (user.displayName) {
    return user.displayName.split(' ').map(n => n[0]).join('').toUpperCase();
  }
  return 'UN';
};

const getDiplayName = (user) => user.displayName || user.email;

const initialPosts = [
  { id: 'p1', image: 'https://placehold.co/200x130/4F46E5/ffffff?text=Post+1' },
  { id: 'p2', image: 'https://placehold.co/200x130/10B981/ffffff?text=Post+2' },
  { id: 'p3', image: 'https://placehold.co/200x130/F59E0B/ffffff?text=Post+3' },
  { id: 'p4', image: 'https://placehold.co/200x130/EF4444/ffffff?text=Post+4' },
  { id: 'p5', image: 'https://placehold.co/200x130/3B82F6/ffffff?text=Post+5' },
  { id: 'p6', image: 'https://placehold.co/200x130/14B8A6/ffffff?text=Post+6' },
  { id: 'p7', image: 'https://placehold.co/200x130/A855F7/ffffff?text=Post+7' },
];

const postEmpty = "https://placehold.co/100x100/EEEEEE/999999?text=No+Images";

// --- MOCK COMPONENTS (Replacing MUI) ---

// Uses lucide icons
const MenuEditDelete = ({ Edit, Delete }) => {
  return (
    <div className="flex flex-col items-center bg-white/90 rounded-xl shadow-lg border border-gray-100 p-0.5">
      <button
        onClick={Edit}
        className="text-gray-700 hover:text-blue-600 p-1 rounded-full transition duration-150 hover:bg-gray-100"
        title="Edit Post"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-edit"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
      </button>
      <button
        onClick={Delete}
        className="text-gray-700 hover:text-red-600 p-1 rounded-full transition duration-150 hover:bg-gray-100"
        title="Delete Post"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
      </button>
    </div>
  );
};

// --- MAIN APPLICATION COMPONENT ---

const App = () => {
  const [myImagePosts, setMyImagePosts] = useState(initialPosts);
  const [postIdToDelete, setPostIdToDelete] = useState(null);

  // Function to simulate opening a post in a gallery view
  const openGalleryForPost = (postId) => {
    console.log(`Opening gallery for post ID: ${postId}`);
    // In a real app, this would open a modal or navigate to a detailed view.
  };

  // 1. Request deletion (opens the confirmation modal)
  const requestDelete = (postId) => {
    setPostIdToDelete(postId);
    console.log(`User requested deletion for post ID: ${postId}`);
  };

  // 2. Execute deletion
  const confirmDelete = useCallback(() => {
    if (postIdToDelete) {
      setMyImagePosts(prevPosts =>
        prevPosts.filter(p => p.id !== postIdToDelete)
      );
      console.log(`CONFIRMED: Post ID ${postIdToDelete} deleted.`);
      setPostIdToDelete(null); // Close modal
    }
  }, [postIdToDelete]);

  // 3. Cancel deletion
  const cancelDelete = useCallback(() => {
    setPostIdToDelete(null);
  }, []);

  return (
    <div className="p-4 bg-gray-50 min-h-screen flex justify-center items-start pt-10">
      <div className="w-full max-w-4xl bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-200">

        {/* The user's provided component structure, translated to Tailwind */}
        <div className="p-4 border border-gray-200 rounded-lg">
          <h2 className="text-xl font-semibold mb-2 text-gray-800">
            My Post
          </h2>

          {/* User Info Bar */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 mb-4">
              {/* Avatar and Info Stack */}
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-lg`}
                style={{ backgroundColor: theme.palette.primary.main }}
              >
                {user.photo ? (
                  <img
                    src={user.photo}
                    alt="User Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  getInitials(user)
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-900">
                  {getDiplayName(user)}
                </span>
                <span className="text-xs text-indigo-500">
                  VIII-A
                </span>
              </div>
            </div>

            {/* Status Badge */}
            <div>
              <span className="relative inline-flex items-center">
                <span
                  className="absolute top-0 right-0 h-2 w-2 rounded-full ring-2 ring-white"
                  style={{ backgroundColor: theme.palette.success.main }}
                ></span>
                <span
                  className="text-xs font-semibold px-2 py-0.5 rounded-full"
                  style={{
                    backgroundColor: theme.palette.success.lighter,
                    color: theme.palette.success.main,
                  }}
                >
                  Active Now
                </span>
              </span>
            </div>
          </div>

          <hr className="my-2 border-gray-200" />

          {/* Post Gallery Container */}
          <div
            className="overflow-y-auto pr-2"
            style={{ maxHeight: 'calc(100vh - 250px)' }}
          >
            <div className="grid gap-4 mt-4 grid-cols-2 sm:grid-cols-3 md:grid-cols-4">
              {myImagePosts.length === 0 && (
                <div className="col-span-full flex flex-col items-center justify-center py-12 h-64">
                  <img width={100} src={postEmpty} alt="No images posted" className="opacity-70 mb-2" />
                  <p className="text-sm text-gray-500">
                    No images posted yet
                  </p>
                </div>
              )}

              {myImagePosts.map((p) => (
                <div key={p.id} className="relative group overflow-hidden">
                  {/* MenuEditDelete Component - Styled to appear on hover/always visible */}
                  <div
                    className="absolute top-2 right-2 z-10 transition-opacity duration-300 opacity-100 group-hover:opacity-100"
                  >
                    <MenuEditDelete
                      Edit={() => console.log(`Editing post ${p.id}`)}
                      Delete={() => requestDelete(p.id)}
                    />
                  </div>

                  {/* Image Thumbnail */}
                  <img
                    className="w-full h-32 object-cover rounded-lg shadow-md border border-gray-300 cursor-pointer transition transform duration-200 group-hover:scale-[1.01] group-hover:shadow-xl"
                    src={p.image || undefined}
                    alt="Post preview"
                    onClick={() => openGalleryForPost(p.id)}
                    onError={(e) => { e.target.onerror = null; e.target.src="https://placehold.co/200x130/EF4444/ffffff?text=Error"; }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal (Replaces alert()) */}
      {postIdToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-2xl p-6 max-w-sm w-full">
            <h3 className="text-xl font-bold text-red-600 mb-3">Confirm Deletion</h3>
            <p className="text-gray-700 mb-6">
              Are you sure you want to permanently delete post ID: **{postIdToDelete}**? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 text-sm font-medium text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition shadow-md"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default App;
