/// <reference lib="webworker" />
export {}; // <-- IMPORTANT: must be the first real statement
/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-restricted-globals */
// eslint-disable-next-line @typescript-eslint/no-unused-expressions, no-underscore-dangle
// self.__WB_MANIFEST

/* eslint-disable no-restricted-globals */
/* global __APP_VERSION__ */
// --------------------------------------
// Cache Versioning
// --------------------------------------
const CACHE_VERSION = (typeof __APP_VERSION__ === 'string' && __APP_VERSION__) || 'v0.0.0';
const PRECACHE = `precache-${CACHE_VERSION}`;
const RUNTIME = `runtime-${CACHE_VERSION}`;
// eslint-disable-next-line @typescript-eslint/no-unused-expressions, no-underscore-dangle
// const PRECACHE_URLS = self.__WB_MANIFEST || [];

// REQUIRED for injectManifest
self.__WB_MANIFEST;

// --------------------------------------
// Important static files ONLY
// --------------------------------------
const PRECACHE_URLS = [
  '/', // main HTML
  '/index.html',
  // '/manifest.webmanifest',
  '/logo-small.svg',
  '/favicon.ico',
  // '/offline.html', // optional offline fallback page
];

// --------------------------------------
// Install
// --------------------------------------
self.addEventListener('install', (event) => {
  event.waitUntil(caches.open(PRECACHE).then((cache) => cache.addAll(PRECACHE_URLS)));
  self.skipWaiting();
});

// --------------------------------------
// Activate: remove old caches
// --------------------------------------
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches
      .keys()
      .then((keys) =>
        Promise.all(keys.filter((key) => ![PRECACHE, RUNTIME].includes(key)).map((key) => caches.delete(key)))
      )
  );
  self.clients.claim();
});

// --------------------------------------
// Fetch: Runtime caching
// --------------------------------------
self.addEventListener('fetch', (event) => {
  if (event.request.method !== 'GET') return;

  event.respondWith(
    caches.match(event.request).then((cached) => {
      if (cached) return cached;

      return caches.open(RUNTIME).then((cache) =>
        fetch(event.request)
          .then((response) => {
            if (response.ok) cache.put(event.request, response.clone());
            return response;
          })
          .catch(() => caches.match('/index.html'))
      );
    })
  );
});
