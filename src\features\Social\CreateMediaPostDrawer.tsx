import React, { useState, useEffect, useMemo } from 'react';
import {
  Drawer,
  Box,
  Typo<PERSON>,
  IconButton,
  Divider,
  TextField,
  Button,
  FormControlLabel,
  Switch,
  Tooltip,
  useTheme,
  ToggleButtonGroup,
  Snackbar,
  Alert,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import VideocamIcon from '@mui/icons-material/Videocam';
import { RiShareForwardLine } from 'react-icons/ri';
import { ToggleButton } from '@mui/material';
import { currentUser, Post } from './Social';
import { SendOutlinedIcon } from '@/theme/overrides/CustomIcons';

interface CreateMediaPostDrawerProps {
  open: boolean;
  onClose: () => void;
  onPostCreate: (post: Post) => void;
}

const CreateMediaPostDrawer: React.FC<CreateMediaPostDrawerProps> = ({ open, onClose, onPostCreate }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<'Image' | 'Video' | 'Text'>('Text');
  const [postText, setPostText] = useState('');
  const [postMedia, setPostMedia] = useState<string | null>(null);
  const [commentsDisabled, setCommentsDisabled] = useState(false);
  const [mediaType, setMediaType] = useState<'image' | 'video' | null>(null);

  // Snackbar states
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'error' | 'info' | 'success' | 'warning'>('info');

  const showSnackbar = (message: string, severity: 'error' | 'info' | 'success' | 'warning' = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = (_?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') return;
    setSnackbarOpen(false);
  };

  useEffect(() => {
    if (open) {
      setPostText('');
      setPostMedia(null);
      setCommentsDisabled(false);
      setMediaType(null);
      setActiveTab('Text');
    }
  }, [open]);

  // Handle media change (image/video upload)
  const handleMediaChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: 'image' | 'video',
    activeTabShow: 'Image' | 'Video' | 'Text'
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setActiveTab(activeTabShow);
    const mediaUrl = URL.createObjectURL(file);
    setPostMedia(mediaUrl);
    setMediaType(type);

    // 🎥 Video validation
    if (type === 'video') {
      const videoElement = document.createElement('video');
      videoElement.src = mediaUrl;

      videoElement.onloadedmetadata = () => {
        if (videoElement.duration > 60) {
          showSnackbar('Please select a video shorter than 60 seconds.', 'error');
          setPostMedia(null);
          setMediaType(null);
        }
      };
    }

    // 🖼️ Image validation
    if (type === 'image') {
      if (file.size > 5 * 1024 * 1024) {
        showSnackbar('Please select an image smaller than 5MB.', 'error');
        setPostMedia(null);
        setMediaType(null);
      }
    }
  };

  const handleRemoveMedia = () => {
    setPostMedia(null);
    setMediaType(null);
    showSnackbar('Media removed successfully.', 'info');
  };

  const createPost = (isDraft = false) => {
    if (!postText.trim() && !postMedia) {
      showSnackbar('Please add text or media before posting.', 'warning');
      return;
    }

    const newPost: Post = {
      id: Date.now(),
      userName: currentUser.userName,
      className: currentUser.className,
      avatar: currentUser.avatar,
      text: postText,
      image: mediaType === 'image' ? postMedia : null,
      video: mediaType === 'video' ? postMedia : null,
      likes: 0,
      liked: false,
      comments: [],
      isMine: true,
      likedByUsers: [],
      commentsDisabled,
      isDraft,
      approved: (currentUser.className === 'Admin' || currentUser.className === 'Teacher') ?? false, // Auto-approve if Admin
    };

    onPostCreate(newPost);
    showSnackbar('Post shared successfully!', 'success');
    onClose();
  };

  const isContentReady = postText.trim() || postMedia;

  const handleTabChange = (_: React.MouseEvent<HTMLElement>, newValue: 'Image' | 'Video' | 'Text' | null) => {
    if (!newValue) return;
    if (postMedia && newValue !== activeTab) {
      showSnackbar('Please remove current media to switch type.', 'warning');
    } else {
      setActiveTab(newValue);
    }
  };

  const tabContent = useMemo(() => {
    if (postMedia) {
      return (
        <Box>
          {mediaType === 'image' && (
            <Box mt={2} sx={{ position: 'relative' }}>
              <img
                src={postMedia}
                alt="preview"
                style={{
                  width: '100%',
                  borderRadius: 12,
                  maxHeight: '300px',
                  objectFit: 'cover',
                }}
              />
              <Tooltip title={`Remove ${mediaType}`}>
                <IconButton
                  onClick={handleRemoveMedia}
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    bgcolor: 'rgba(0, 0, 0, 0.5)',
                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' },
                  }}
                >
                  <CloseIcon sx={{ color: 'white' }} />
                </IconButton>
              </Tooltip>
            </Box>
          )}
          {mediaType === 'video' && (
            <video
              src={postMedia}
              controls
              preload="metadata"
              style={{
                width: '100%',
                maxHeight: '300px',
                objectFit: 'cover',
                borderRadius: 12,
              }}
            />
          )}
          <TextField
            fullWidth
            multiline
            placeholder="Add a caption..."
            value={postText}
            onChange={(e) => setPostText(e.target.value)}
            sx={{ mt: 2, '& .MuiOutlinedInput-root': { borderRadius: '12px' } }}
          />
        </Box>
      );
    }

    switch (activeTab) {
      case 'Image':
        return (
          <Box sx={{ textAlign: 'center', py: 10 }}>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              No media selected.
            </Typography>
            <Button component="label" variant="contained" startIcon={<PhotoCameraIcon />} size="large">
              Select Photo
              <input
                hidden
                accept="image/*"
                type="file"
                capture="environment" // 👈 Opens camera directly on Android if available
                onChange={(e) => handleMediaChange(e, 'image', 'Image')}
              />
            </Button>
          </Box>
        );
      case 'Video':
        return (
          <Box sx={{ textAlign: 'center', py: 10 }}>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              No media selected.
            </Typography>
            <Button component="label" variant="contained" startIcon={<VideocamIcon />} size="large">
              Select Video
              <input
                hidden
                accept="video/*"
                type="file"
                capture="camcorder" // 👈 Opens video camera directly
                onChange={(e) => handleMediaChange(e, 'video', 'Video')}
              />
            </Button>
          </Box>
        );
      default:
        return (
          <TextField
            fullWidth
            multiline
            placeholder="Share a post with text..."
            value={postText}
            onChange={(e) => setPostText(e.target.value)}
            sx={{ '& .MuiOutlinedInput-root': { borderRadius: '12px' }, mt: 2 }}
          />
        );
    }
  }, [activeTab, postMedia, postText, mediaType, handleMediaChange, handleRemoveMedia]);

  return (
    <>
      <Drawer
        anchor="bottom"
        open={open}
        onClose={onClose}
        PaperProps={{
          sx: {
            height: '100vh',
            backgroundColor: theme.palette.background.paper,
          },
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            px: 2,
            py: 2,
            position: 'relative',
          }}
        >
          <IconButton onClick={onClose} sx={{ position: 'absolute', right: 2, top: 2 }}>
            <CloseIcon />
          </IconButton>
          <Typography textAlign="center" variant="subtitle2" fontFamily="Poppins Semibold">
            New Post
          </Typography>
        </Box>

        <Divider />

        <Box sx={{ p: 2, pb: 2, overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}>
          {tabContent}
          <Box my={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={commentsDisabled}
                  onChange={(e) => setCommentsDisabled(e.target.checked)}
                  color="primary"
                />
              }
              label="Disable Comments"
            />
          </Box>
        </Box>

        <Box
          sx={{
            position: 'fixed',
            bottom: 90,
            left: 0,
            right: 0,
            display: 'flex',
            justifyContent: 'center',
            zIndex: 11,
          }}
        >
          <Button
            variant="contained"
            onClick={() => createPost(false)}
            disabled={!isContentReady}
            endIcon={<RiShareForwardLine />}
            sx={{
              borderRadius: '999px',
              px: 4,
              py: 1.2,
              fontWeight: 600,
              textTransform: 'none',
              boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
            }}
          >
            Share
          </Button>
        </Box>

        <Box
          sx={{
            position: 'fixed',
            bottom: 16,
            left: 0,
            right: 0,
            display: 'flex',
            justifyContent: 'center',
            zIndex: 12,
            backdropFilter: 'blur(10px)',
          }}
        >
          <ToggleButtonGroup
            exclusive
            fullWidth
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              border: 0,
              display: 'flex',
              gap: 1,
              bgcolor: 'rgba(255,255,255,0.8)',
              borderRadius: 10,
              p: 0,
              mx: 2,
              boxShadow: 4,
              '& .MuiToggleButtonGroup-grouped': {
                border: 'none',
                borderRadius: '50px !important',
              },
            }}
          >
            <ToggleButton
              value="Text"
              size="small"
              disableRipple
              disableFocusRipple
              disableTouchRipple
              sx={{
                px: 3,
                textTransform: 'none',
                fontFamily: activeTab === 'Text' ? 'Poppins Bold' : 'Poppins Semibold',
                fontSize: 15,
                color: activeTab === 'Text' ? 'white' : 'black',
                bgcolor: activeTab === 'Text' ? theme.palette.primary.main : 'transparent',
                '&:hover': {
                  bgcolor: activeTab === 'Text' ? theme.palette.primary.dark : '#f0f0f0',
                },
              }}
            >
              Text
            </ToggleButton>

            <ToggleButton
              value="Image"
              size="small"
              component="label"
              disableRipple
              disableFocusRipple
              disableTouchRipple
              sx={{
                px: 3,
                textTransform: 'none',
                fontFamily: activeTab === 'Image' ? 'Poppins Bold' : 'Poppins Semibold',
                fontSize: 15,
                color: activeTab === 'Image' ? 'white' : 'black',
                bgcolor: activeTab === 'Image' ? theme.palette.primary.main : 'transparent',
                '&:hover': {
                  bgcolor: activeTab === 'Image' ? theme.palette.primary.dark : '#f0f0f0',
                },
              }}
            >
              Image
            </ToggleButton>

            <ToggleButton
              value="Video"
              size="small"
              component="label"
              disableRipple
              disableFocusRipple
              disableTouchRipple
              sx={{
                px: 3,
                textTransform: 'none',
                fontFamily: activeTab === 'Video' ? 'Poppins Bold' : 'Poppins Semibold',
                fontSize: 15,
                color: activeTab === 'Video' ? 'white' : 'black',
                bgcolor: activeTab === 'Video' ? theme.palette.primary.main : 'transparent',
                '&:hover': {
                  bgcolor: activeTab === 'Video' ? theme.palette.primary.dark : '#f0f0f0',
                },
              }}
            >
              Video
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </Drawer>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={snackbarSeverity} variant="standard" sx={{ width: '100%' }} onClose={handleSnackbarClose}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default CreateMediaPostDrawer;
