import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import {
  DescriptiveExamDataType,
  DescriptiveExamRequest,
  ObjectiveExamDataType,
  ObjectiveExamRequest,
} from '@/types/OnlineExam';

async function GetObjectiveExamList(request: ObjectiveExamRequest): Promise<APIResponse<ObjectiveExamDataType[]>> {
  const response = await privateApi.post<ObjectiveExamDataType[]>(ApiUrls.GetObjectiveExamList, request);
  console.log('response', response);
  return response;
}

async function GetDescriptiveExamList(
  request: DescriptiveExamRequest
): Promise<APIResponse<DescriptiveExamDataType[]>> {
  const response = await privateApi.post<DescriptiveExamDataType[]>(ApiUrls.GetDescriptiveExamList, request);
  console.log('response', response);
  return response;
}

const methods = {
  GetObjectiveExamList,
  GetDescriptiveExamList,
};

export default methods;
