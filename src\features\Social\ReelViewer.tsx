/* eslint-disable no-nested-ternary */
import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Stack,
  IconButton,
  Typography,
  Dialog,
  useMediaQuery,
  useTheme,
  Avatar,
  Collapse,
  SwipeableDrawer,
  Drawer,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import { RiChat1Line } from 'react-icons/ri';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Link } from 'react-router-dom';
import { Post } from './Social';
import { HeartFilledIcon, HeartOutlinedIcon, SendOutlinedIcon } from '@/theme/overrides/CustomIcons';
import CommentsDrawer from './CommentDrawer';

interface ReelViewerProps {
  open: boolean;
  onClose: () => void;
  posts: Post[];
  startIndex: number;
  onLike: (postId: number) => void;
  onOpenComments: (postId: number) => void;
  initialTime?: number;
  initialMuted?: boolean;
  setOpenSearch?: any;
}

const ReelViewer: React.FC<ReelViewerProps> = ({
  open,
  onClose,
  posts,
  startIndex,
  onLike,
  onOpenComments,
  initialTime,
  initialMuted,
}) => {
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout[]>([]);
  const swipeContainerRef = useRef<HTMLDivElement>(null);

  const [currentIndex, setCurrentIndex] = useState(startIndex);
  const [mutedStates, setMutedStates] = useState<boolean>(initialMuted ?? true);
  const [iconVisibleStates, setIconVisibleStates] = useState<boolean[]>([]);
  const [expandedMap, setExpandedMap] = useState<Record<number, boolean>>({});
  const [openComments, setOpenComments] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);

  const theme = useTheme();
  const isWebView = useMediaQuery(theme.breakpoints.up('md')); // 👈 detect desktop view

  const touchStart = useRef(0);
  const touchEnd = useRef(0);
  const minSwipeDistance = 50;
  const isInitialLoad = useRef(false);

  const videoPosts = posts.filter((p) => p.video);

  // Initialize icons
  useEffect(() => {
    setIconVisibleStates(Array(videoPosts.length).fill(false));
  }, [videoPosts.length]);

  // When open, set current index
  useEffect(() => {
    if (open) {
      setCurrentIndex(startIndex);
      isInitialLoad.current = true;
    }
  }, [open, startIndex]);

  // Video playback control
  useEffect(() => {
    if (!open) {
      videoRefs.current.forEach((v) => v?.pause());
      return;
    }

    videoRefs.current.forEach((video, i) => {
      if (!video) return;
      video.muted = mutedStates;
      if (i === currentIndex) {
        if (isInitialLoad.current) {
          video.currentTime = initialTime || 0;
          isInitialLoad.current = false;
        }
        video.play().catch(() => {});
      } else {
        video.pause();
        video.currentTime = 0;
      }
    });
  }, [open, currentIndex, mutedStates, initialTime]);

  useEffect(() => {
    return () => timeoutRef.current.forEach(clearTimeout);
  }, []);

  const handleIndexChange = (newIndex: number) => {
    if (newIndex >= 0 && newIndex < videoPosts.length) {
      setCurrentIndex(newIndex);
    }
  };

  // Swipe gesture
  const onTouchStart = (e: React.TouchEvent) => {
    touchEnd.current = 0;
    touchStart.current = e.targetTouches[0].clientY;
  };
  const onTouchMove = (e: React.TouchEvent) => {
    touchEnd.current = e.targetTouches[0].clientY;
  };
  const onTouchEnd = () => {
    if (touchStart.current === 0 || touchEnd.current === 0) return;
    const distance = touchStart.current - touchEnd.current;

    if (distance > minSwipeDistance) handleIndexChange(currentIndex + 1);
    else if (distance < -minSwipeDistance) handleIndexChange(currentIndex - 1);

    touchStart.current = 0;
    touchEnd.current = 0;
  };

  // Mute / Unmute
  const handleVideoClick = (index: number) => {
    const newMuted = !mutedStates;
    setMutedStates(newMuted);
    videoRefs.current.forEach((video) => {
      if (video) video.muted = newMuted;
    });

    const newIconVisibleStates = Array(videoPosts.length).fill(false);
    newIconVisibleStates[index] = true;
    setIconVisibleStates(newIconVisibleStates);

    if (timeoutRef.current[index]) clearTimeout(timeoutRef.current[index]);
    timeoutRef.current[index] = setTimeout(() => {
      setIconVisibleStates((prev) => {
        const newStates = [...prev];
        newStates[index] = false;
        return newStates;
      });
    }, 800);
  };

  const handleCommentIconClick = (post: Post) => {
    setSelectedPost(post);
    setOpenComments(true);
  };

  const videoContent = (
    <Stack sx={{ height: '100vh', position: 'relative' }}>
      <Box
        ref={swipeContainerRef}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
        sx={{
          height: '100%',
          width: '100%',
          overflow: 'hidden',
          transition: 'all 0.4s ease',
          transform: openComments ? 'scale(0.75) translateY(-25%)' : 'scale(1)',
          transformOrigin: 'top center',
        }}
      >
        <Box
          sx={{
            height: '100%',
            transform: `translateY(-${currentIndex * 100}%)`,
            transition: 'transform 0.3s',
          }}
        >
          {videoPosts.map((post, i) => {
            const expanded = expandedMap[post.id] || false;
            const MAX_LENGTH = isWebView ? 100 : 35;
            const captionPreview =
              post?.caption?.length > MAX_LENGTH && !expanded
                ? post?.caption?.slice(0, MAX_LENGTH) + '...'
                : post?.caption;

            return (
              <Box
                key={post.id}
                sx={{
                  position: 'relative',
                  height: openComments ? '100vh' : '100vh',
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  overflow: 'hidden',
                }}
              >
                <video
                  ref={(el) => (videoRefs.current[i] = el)}
                  src={post.video}
                  style={{
                    width: '100%',
                    height: openComments ? '50%' : '100%',
                    objectFit: 'contain',
                    cursor: 'pointer',
                    // filter: openComments ? 'brightness(40%)' : 'brightness(100%)',
                    transition: 'filter 0.3s ease',
                  }}
                  loop
                  playsInline
                  autoPlay
                  muted={mutedStates}
                  onClick={() => handleVideoClick(i)}
                />

                {/* 🔊 Mute Icon */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    borderRadius: '50%',
                    p: 2,
                    display: 'flex',
                    opacity: iconVisibleStates[i] ? 1 : 0,
                    transition: 'opacity 0.3s',
                    pointerEvents: 'none',
                  }}
                >
                  {mutedStates ? (
                    <VolumeOffIcon sx={{ color: 'white', fontSize: 30 }} />
                  ) : (
                    <VolumeUpIcon sx={{ color: 'white', fontSize: 30 }} />
                  )}
                </Box>

                {/* ❤️ 💬 ↗ Icons */}
                {!openComments && (
                  <Stack
                    spacing={2}
                    sx={{
                      position: 'absolute',
                      right: 10,
                      bottom: 100,
                      color: 'white',
                      alignItems: 'center',
                    }}
                  >
                    <Stack alignItems="center">
                      <IconButton size="small" color="inherit" onClick={() => onLike(post.id)}>
                        {post.liked ? (
                          <HeartFilledIcon fontSize="large" color="error" />
                        ) : (
                          <HeartOutlinedIcon fontSize="large" />
                        )}
                      </IconButton>
                      <Typography variant="subtitle2" lineHeight={1}>
                        {post.likes > 0 && post.likes}
                      </Typography>
                    </Stack>

                    <Stack alignItems="center">
                      <IconButton size="small" color="inherit" onClick={() => handleCommentIconClick(post)}>
                        <RiChat1Line fontSize="30px" />
                      </IconButton>
                      <Typography variant="subtitle2" lineHeight={1}>
                        {post.comments.length > 0 && post.comments.length}
                      </Typography>
                    </Stack>

                    <Stack alignItems="center">
                      <IconButton size="small" color="inherit">
                        <SendOutlinedIcon fontSize="medium" />
                      </IconButton>
                      <Typography variant="subtitle2" lineHeight={1}>
                        2k
                      </Typography>
                    </Stack>
                  </Stack>
                )}
                {/* ❌ Close */}
                <IconButton
                  onClick={onClose}
                  sx={{
                    position: 'absolute',
                    top: 20,
                    right: 10,
                    color: 'white',
                    bgcolor: 'rgba(0,0,0,0.5)',
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' },
                    zIndex: 10,
                  }}
                >
                  <CloseIcon />
                </IconButton>

                {/* 🧾 Caption */}
                {!openComments && (
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 30,
                      left: 10,
                      color: 'white',
                      textShadow: '1px 1px 3px rgba(0,0,0,0.5)',
                    }}
                  >
                    <Stack direction="row" alignItems="center" gap={1} mb={1}>
                      <Avatar src={post.avatar} alt={post.userName} sx={{ width: 45, height: 45, cursor: 'pointer' }} />
                      <Box textAlign="start">
                        <Typography variant="subtitle2" fontFamily="Poppins Semibold">
                          {post.userName}
                        </Typography>
                        <Typography
                          lineHeight={1}
                          variant="subtitle2"
                          fontFamily="Poppins Semibold"
                          color="text.secondary"
                          fontSize={11}
                        >
                          {post.className}
                        </Typography>
                      </Box>
                    </Stack>

                    {post.caption && (
                      <Box>
                        <Collapse in={expanded} collapsedSize={20}>
                          <Typography variant="caption" sx={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
                            {captionPreview}
                            {post.caption.length > MAX_LENGTH && (
                              <Link
                                component="button"
                                onClick={() =>
                                  setExpandedMap((prev) => ({
                                    ...prev,
                                    [post.id]: !expanded,
                                  }))
                                }
                                sx={{
                                  fontSize: '0.75rem',
                                  fontWeight: 500,
                                  ml: 0.5,
                                  textTransform: 'lowercase',
                                  color: 'text.secondary',
                                  display: 'inline-block',
                                }}
                              >
                                {expanded ? 'less' : 'more'}
                              </Link>
                            )}
                          </Typography>
                        </Collapse>
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>
      </Box>

      {/* ⬆⬇ Scroll Buttons (Desktop Only) */}
      {isWebView && (
        <Box
          sx={{
            position: 'absolute',
            right: 20,
            top: '50%',
            transform: 'translateY(-50%)',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            zIndex: 20,
          }}
        >
          <IconButton
            sx={{
              bgcolor: 'rgba(0,0,0,0.5)',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' },
            }}
            disabled={currentIndex === 0}
            onClick={() => handleIndexChange(currentIndex - 1)}
          >
            <KeyboardArrowUpIcon />
          </IconButton>

          <IconButton
            sx={{
              bgcolor: 'rgba(0,0,0,0.5)',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' },
            }}
            disabled={currentIndex === videoPosts.length - 1}
            onClick={() => handleIndexChange(currentIndex + 1)}
          >
            <KeyboardArrowDownIcon />
          </IconButton>
        </Box>
      )}

      {/* 🧩 Instagram Comments Drawer */}
      <CommentsDrawer
        post={selectedPost}
        open={openComments}
        onClose={() => setOpenComments(false)}
        onAddComment={(postId, text) => console.log('Comment added:', postId, text)}
        heightReelView="70vh"
      />
    </Stack>
  );

  return isWebView ? (
    <Dialog
      fullScreen
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { backgroundColor: 'black', overflow: 'hidden', position: 'relative' },
      }}
    >
      {videoContent}
    </Dialog>
  ) : (
    <Drawer
      anchor="bottom"
      open={open}
      onClose={onClose}
      // onOpen={() => {}}
      PaperProps={{
        sx: {
          height: '100vh',
          bgcolor: 'black',
          overflow: 'hidden',
        },
      }}
    >
      {videoContent}
    </Drawer>
  );
};

export default ReelViewer;
