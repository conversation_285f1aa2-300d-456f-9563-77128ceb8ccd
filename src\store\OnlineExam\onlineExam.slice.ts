import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { ObjectiveExamDataType, OnlineExamState } from '@/types/OnlineExam';
import { flushStore } from '../flush.slice';
import { fetchDescriptiveExamList, fetchObjectiveExamList } from './onlineExam.thunks';

const initialState: OnlineExamState = {
  objectiveExamList: {
    data: [],
    status: 'idle',
    error: null,
  },
  descriptiveExamList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const onlineExamSlice = createSlice({
  name: 'onlineExam',
  initialState,
  reducers: {
    setObjectiveExamList: (state, action: PayloadAction<ObjectiveExamDataType[]>) => {
      state.objectiveExamList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching online exam
      // ------ Get Objective Exam List------- //
      .addCase(fetchObjectiveExamList.pending, (state) => {
        state.objectiveExamList.status = 'loading';
        state.objectiveExamList.error = null;
      })
      .addCase(fetchObjectiveExamList.fulfilled, (state, action) => {
        state.objectiveExamList.status = 'success';
        state.objectiveExamList.data = action.payload;
        state.objectiveExamList.error = null;
      })
      .addCase(fetchObjectiveExamList.rejected, (state, action) => {
        state.objectiveExamList.status = 'error';
        state.objectiveExamList.error = action.payload || 'Unknown error in fetching Objetive Exam List';
      })
      // ------ Get Descriptive Exam List------- //
      .addCase(fetchDescriptiveExamList.pending, (state) => {
        state.descriptiveExamList.status = 'loading';
        state.descriptiveExamList.error = null;
      })
      .addCase(fetchDescriptiveExamList.fulfilled, (state, action) => {
        state.descriptiveExamList.status = 'success';
        state.descriptiveExamList.data = action.payload;
        state.descriptiveExamList.error = null;
      })
      .addCase(fetchDescriptiveExamList.rejected, (state, action) => {
        state.descriptiveExamList.status = 'error';
        state.descriptiveExamList.error = action.payload || 'Unknown error in fetching Descriptive Exam List';
      })

      .addCase(flushStore, () => initialState);
  },
});

export const { setObjectiveExamList } = onlineExamSlice.actions;

export default onlineExamSlice.reducer;
