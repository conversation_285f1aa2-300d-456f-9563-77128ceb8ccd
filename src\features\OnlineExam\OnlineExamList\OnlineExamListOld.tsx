/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  ToggleButtonGroup,
  ToggleButton,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

import { MdAdd } from 'react-icons/md';
import CreateOnlineExam from './CreateOnlineExam';
import MapOnlineExam from './MapOnlineExam';
import DescExamList from '../DescriptiveExam/DescExamList/DescExamList';
import ObjExamList from '../ObjectiveExam/ObjExamList/ObjExamList';
import ViewObjExam from '../ObjectiveExam/ObjExamList/ViewObjExam';

export const data = [
  {
    SlNo: 1,
    Class: '10 A',
    Subject: 'COMPUTER',
    Exam: 'SECOND MID TERM EXAM',
    Type: 'Objective(MCQ) Type',
    StartTime: '11 Nov 2020 08:15 PM',
    EndTime: '11 Nov 2020 08:45 PM',
    AddedQuestions: '20 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 2,
    Class: '10 A',
    Subject: 'CHEMISTRY',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 10:00 AM',
    EndTime: '29 Aug 2020 11:10 AM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 3,
    Class: '5 B',
    Subject: 'MALAYALAM',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '28 Aug 2020 08:15 PM',
    EndTime: '28 Aug 2020 10:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 4,
    Class: '10 C',
    Subject: 'ENGLISH',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '27 Aug 2020 08:15 PM',
    EndTime: '27 Aug 2020:10:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 5,
    Class: '3 A',
    Subject: 'BIOLOGY',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 09:15 AM',
    EndTime: '29 Aug 2020 11:10 AM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 6,
    Class: '7 C',
    Subject: 'COMPUTER',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '26 Aug 2020 08:15 PM',
    EndTime: '26 Aug 2020 09:45 PM',
    AddedQuestions: '30 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 7,
    Class: '10 A',
    Subject: 'SOCIAL',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 02:00 PM',
    EndTime: '29 Aug 2020 03:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 8,
    Class: '4 A',
    Subject: 'HINDI',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 11:00 AM',
    EndTime: '29 Aug 2020 12:10 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 9,
    Class: '9 B',
    Subject: 'MATHS',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Objective(MCQ) Type',
    StartTime: '29 Aug 2020 12:00 PM',
    EndTime: '29 Aug 2020 01:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 10,
    Class: '1 A',
    Subject: 'PHYSICS',
    Exam: 'FIRST TERMINAL EXAMINATION 2020-21',
    Type: 'Descriptive Type',
    StartTime: '29 Aug 2020 01:00 PM',
    EndTime: '29 Aug 2020 02:00 PM',
    AddedQuestions: '15 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 11,
    Class: '10 C',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 13',
    Type: 'Objective(MCQ) Type',
    StartTime: '11 Aug 2020 06:00 PM',
    EndTime: '11 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 12,
    Class: '2 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 12',
    Type: 'Descriptive Type',
    StartTime: '10 Aug 2020 06:00 PM',
    EndTime: '10 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 13,
    Class: '8 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 11',
    Type: 'Descriptive Type',
    StartTime: '07 Aug 2020 06:00 PM',
    EndTime: '07 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 14,
    Class: '10 A',
    Subject: 'GK',
    Exam: 'GK MOCK TEST 10',
    Type: 'Objective(MCQ) Type',
    StartTime: '06 Aug 2020 06:00 PM',
    EndTime: '06 Aug 2020 09:00 PM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
  {
    SlNo: 15,
    Class: '10 B',
    Subject: 'GK',
    Exam: 'GK MOCK TEST B',
    Type: 'Descriptive Type',
    StartTime: '26 Jul 2020 07:00 AM',
    EndTime: '26 Jul 2020 08:30 AM',
    AddedQuestions: '10 Questions',
    Staff: 'Passdaily Support',
    Status: 'Published',
  },
];

const OnlineExamListOldRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function OnlineExamListOld() {
  const theme = useTheme();
  // const { themeMode } = useSettings();
  // const isLight = themeMode === 'light';
  // const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  // const [view, setView] = useState(false);
  const [view, setView] = useState<'ObjExamList' | 'DescExamList' | 'ViewObjExam' | 'DescObjExam'>('ObjExamList');
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);
  const [mapPopup, setMapPopup] = useState(false);

  const handleClickCloseDelete = () => setDelete(false);

  const [examType, setExamType] = React.useState('obj');

  const handleClickView = () => setView('ViewObjExam');
  const handleClickCloseView = () => setView('ObjExamList');

  const handleChange = (event: React.MouseEvent<HTMLElement>, newExamType: string) => {
    setExamType(newExamType);
  };
  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {OnlineExamListOldColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return view === 'ViewObjExam' ? (
    <ViewObjExam onBackClick={handleClickCloseView} />
  ) : (
    <Page title="Schedule List">
      <OnlineExamListOldRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20}>
              Manage Exams
            </Typography>
            <ToggleButtonGroup value={examType} exclusive onChange={handleChange} aria-label="Platform" size="small">
              <ToggleButton value="obj" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Objective(MCQ) Exams
              </ToggleButton>
              <ToggleButton value="desc" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Descriptive Exams
              </ToggleButton>
            </ToggleButtonGroup>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
              <Button
                sx={{ borderRadius: '20px', ml: 1 }}
                size="small"
                variant="contained"
                onClick={() => setMapPopup(true)}
              >
                Map Last Year Exams
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {examType === 'obj' ? <ObjExamList handleClickView={handleClickView} /> : <DescExamList />}
          </div>
        </Card>
      </OnlineExamListOldRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      {/* <Popup size="lg" state={view} popupContent={<ViewOnlineExam onClose={() => handleClickCloseView()} />} /> */}
      <Popup
        size="md"
        title="Create New Online Exam"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateOnlineExam />}
      />
      <Popup
        size="md"
        title="Map Online Exam"
        state={mapPopup}
        onClose={() => setMapPopup(false)}
        popupContent={<MapOnlineExam />}
      />
    </Page>
  );
}

export default OnlineExamListOld;
