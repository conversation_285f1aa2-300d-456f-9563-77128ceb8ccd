/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import {
  Drawer,
  Box,
  IconButton,
  Stack,
  Avatar,
  Typography,
  CardActionArea,
  Button,
  Grid,
  useTheme,
  Tabs,
  Tab,
  Chip,
} from '@mui/material';

// Import necessary icons for the tabs
import MenuIcon from '@mui/icons-material/Menu';
import GridOnIcon from '@mui/icons-material/GridOn';
import TextFieldIcon from '@mui/icons-material/TextFields';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import ShortTextIcon from '@mui/icons-material/ShortText';
import SlowMotionVideoRoundedIcon from '@mui/icons-material/SlowMotionVideoRounded';
import AddIcon from '@mui/icons-material/Add'; // 🎯 NEW: Add Icon
import { currentUser, getInitials, Post, PostCard, User } from './Social';

const ProfileSidebarComponent: React.FC<{
  user: User;
  selectedProfileUser: User;
  myPosts: Post[];
  otherUserProfilePosts: Post[];
  drafts: Post[];
  onOpenGallery: (postId: number) => void;
  onOpenDraftsList: () => void;
  theme: any;
  onOpenReel: (postId: number, currentTime: number, isMuted: boolean) => void;
  setIsprofileReelOpen: (isOpen: boolean) => void;
  setOpenCreateStoryDialog: (isOpen: boolean) => void;
  storiesForDisplay: User[];
  currentTime: number;
  isMuted: boolean;
  setOpenMyPost: (isOpen: boolean) => void;
}> = ({
  user,
  selectedProfileUser,
  myPosts,
  otherUserProfilePosts,
  drafts,
  onOpenGallery,
  onOpenDraftsList,
  theme,
  onOpenReel,
  setIsprofileReelOpen,
  setOpenCreateStoryDialog,
  storiesForDisplay,
  currentTime,
  isMuted,
  setOpenMyPost,
}) => {
  // --- 1. State to manage the active tab ---
  const [activeTab, setActiveTab] = useState('all');

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  // --- 2. Filter posts based on the active tab ---
  const filteredPosts =
    currentUser.userName !== selectedProfileUser.userName
      ? otherUserProfilePosts
      : myPosts.filter((post) => {
          if (post.isDraft) return false; // Always exclude drafts

          switch (activeTab) {
            case 'image':
              return !!post.image;
            case 'video':
              return !!post.video;
            case 'text':
              // Only show posts that are text-only
              return !!post.text && !post.image && !post.video;
            case 'all':
            default:
              return true; // Show all non-draft posts
          }
        });

  return (
    <Stack>
      {/* User Details Card (Unchanged) */}
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" spacing={2}>
          {storiesForDisplay.some((s) => s.userName === user.userName) ? (
            <Stack alignItems="center" sx={{ cursor: 'pointer' }}>
              <Box
                sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 75,
                  height: 75,
                  borderRadius: '50%',
                  background: `conic-gradient(
            ${theme.palette.primary.main},
            ${theme.palette.secondary.main},
            ${theme.palette.primary.main}
          )`, // Instagram-style ring
                  p: 0.5,
                }}
              >
                <Avatar
                  src={selectedProfileUser.avatar || user.avatar}
                  alt={selectedProfileUser.userName || user.userName}
                  onClick={() => setIsprofileReelOpen(true)} // Open story/reel viewer
                  sx={{
                    width: 70,
                    height: 70,
                    border: '2px solid white',
                    cursor: 'pointer',
                  }}
                />
                {selectedProfileUser.userName === currentUser.userName && (
                  <IconButton
                    onClick={() => setOpenCreateStoryDialog(true)}
                    size="small"
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      width: 25,
                      height: 25,
                      border: `2px solid white`,
                      borderRadius: '50%',
                      '&:hover': { bgcolor: theme.palette.primary.dark },
                    }}
                  >
                    <AddIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            </Stack>
          ) : (
            <Stack alignItems="center" sx={{ cursor: 'pointer' }}>
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  onClick={() => setOpenCreateStoryDialog(true)}
                  src={selectedProfileUser.avatar || user.avatar}
                  alt={selectedProfileUser.userName || user.userName}
                  sx={{
                    width: 75,
                    height: 75,
                    bgcolor: theme.palette.grey[100],
                  }}
                />
                {selectedProfileUser.userName === currentUser.userName && (
                  <IconButton
                    onClick={() => setOpenCreateStoryDialog(true)}
                    size="small"
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      width: 25,
                      height: 25,
                      border: `2px solid white`,
                      borderRadius: '50%',
                      '&:hover': { bgcolor: theme.palette.primary.dark },
                    }}
                  >
                    <AddIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            </Stack>
          )}
          <Stack direction="row" spacing={5} width="100%">
            <Box textAlign="start">
              <Typography variant="subtitle2" fontFamily="Poppins Semibold">
                {selectedProfileUser.userName || user.userName}
              </Typography>
              <Typography
                lineHeight={1}
                variant="subtitle2"
                fontFamily="Poppins Semibold"
                color="text.secondary"
                fontSize={11}
              >
                {selectedProfileUser.className || user.className}
              </Typography>
            </Box>
            <Box textAlign="start">
              <Typography variant="subtitle2" fontFamily="Poppins Semibold">
                {otherUserProfilePosts.length || myPosts.length}
              </Typography>
              <Typography
                lineHeight={1}
                variant="subtitle2"
                fontFamily="Poppins Semibold"
                color="text.secondary"
                fontSize={11}
              >
                posts
              </Typography>
            </Box>
          </Stack>
        </Stack>

        <Typography lineHeight={1} variant="subtitle2" fontFamily="Poppins Semibold" fontSize={11}>
          I'm not special, I'm just limited edition.
        </Typography>
        <Typography lineHeight={1} variant="subtitle2" fontFamily="Poppins Semibold" fontSize={11}>
          Capturing moments, creating memories 📷
        </Typography>
      </Stack>

      {/* Drafts Button/Card */}
      {/* {draftCount > 0 && (
        <Card sx={{ borderRadius: 3, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
          <CardActionArea
            onClick={onOpenDraftsList}
            sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                variant="rounded"
                sx={{
                  bgcolor: theme.palette.warning.light,
                  color: theme.palette.warning.dark,
                  width: 48,
                  height: 48,
                }}
              >
                <SaveIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Drafts
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {draftCount} {draftCount === 1 ? 'draft' : 'drafts'}
                </Typography>
              </Box>
            </Stack>
            <Button variant="outlined" color="warning" size="small" sx={{ minWidth: 80 }}>
              View
            </Button>
          </CardActionArea>
        </Card>
      )} */}

      {/* My Post Gallery Preview */}
      {/* <Card sx={{ p: 2, borderRadius: 3, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}> */}
      {/* <Typography
          variant="subtitle1"
          sx={{ fontWeight: 600, mb: 1.5, display: 'flex', alignItems: 'center', gap: 1 }}
        >
          <CollectionsIcon color="primary" fontSize="small" /> My Post Gallery
        </Typography> */}

      {/* --- 3. Tab Navigation UI --- */}
      <Box mt={1} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth" indicatorColor="primary">
          <Tab
            icon={<GridOnIcon sx={{ color: 'black', fontSize: 20 }} />}
            value="all"
            aria-label="all posts"
            sx={{ flex: 1, minWidth: 50, p: '6px 0' }}
          />
          <Tab
            icon={<TextFieldIcon sx={{ color: 'black' }} />}
            value="text"
            aria-label="text posts"
            sx={{ flex: 1, minWidth: 50, p: '6px 0' }}
          />
          <Tab
            icon={<PhotoLibraryIcon sx={{ color: 'black' }} />}
            value="image"
            aria-label="image posts"
            sx={{ flex: 1, minWidth: 50, p: '6px 0' }}
          />
          <Tab
            icon={<SlowMotionVideoRoundedIcon sx={{ color: 'black' }} />}
            value="video"
            aria-label="video posts"
            sx={{ flex: 1, minWidth: 50, p: '6px 0' }}
          />
        </Tabs>
      </Box>

      {/* --- 4. Post Gallery Grid --- */}
      <Grid container spacing={0} my={1} sx={{ height: '100%' }}>
        {filteredPosts.length > 0 ? (
          filteredPosts.map((post) => (
            <Grid item xs={4} key={post.id}>
              <CardActionArea
                onClick={() => {
                  if (activeTab === 'all') {
                    setOpenMyPost(true);
                  } else {
                    post.video
                      ? onOpenReel(post.id, currentTime, isMuted)
                      : post.text
                      ? onOpenGallery(post.id)
                      : onOpenGallery(post.id);
                  }
                }}
                sx={{
                  overflow: 'hidden',
                  border: `1px solid ${theme.palette.divider}`,
                  aspectRatio: activeTab === 'all' ? '5/6' : post.image ? '5/6' : post.video ? '9/16' : '5/6',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {!post.approved && (
                  <Box
                    sx={{
                      p: 1,
                      position: 'absolute',
                      inset: 0,
                      bgcolor: 'rgba(0, 0, 0, 0.4)', // semi-transparent dark overlay
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 10,
                      backdropFilter: 'blur(1px)', // optional: adds a nice blur effect
                    }}
                  >
                    <Chip
                      label="Waiting for approval"
                      color="warning"
                      sx={{
                        fontWeight: 600,
                        fontSize: '0.75rem',
                        px: 0.5,
                        py: 0.5,
                        bgcolor: 'warning.main',
                        color: 'common.white',
                      }}
                    />
                  </Box>
                )}
                {/* Render video if it exists */}
                {post.video ? (
                  <video
                    src={`${post.video}#t=0.1`}
                    muted
                    preload="metadata"
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                ) : // Otherwise, render image if it exists
                post.image ? (
                  <Box
                    component="img"
                    src={post.image}
                    alt="post preview"
                    sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                ) : (
                  // --- 5. Display for Text-Only Posts ---
                  <Box
                    sx={{
                      p: 1,
                      textAlign: 'center',
                      bgcolor: 'background.paper',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <ShortTextIcon sx={{ fontSize: 32, color: 'text.secondary' }} />
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        mt: 1,
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                      }}
                    >
                      {post.text}
                    </Typography>
                  </Box>
                )}
              </CardActionArea>
            </Grid>
          ))
        ) : (
          // --- 6. Empty State Message ---
          <Stack
            justifyContent="center"
            alignItems="center"
            sx={{ width: '100%', height: 'calc(100vh - 273px)', textAlign: 'center', py: 2 }}
          >
            <Typography variant="h6" color="text.secondary">
              No {activeTab} posts yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              When you share {activeTab === 'all' ? 'a post' : `${activeTab}s`}, they will appear here.
            </Typography>
          </Stack>
        )}
      </Grid>
    </Stack>
  );
};

// --- Parent Component That Manages the Drawer (Unchanged) ---

const ProfilePageWithDrawer = ({
  user,
  selectedProfileUser,
  myPosts,
  otherUserProfilePosts,
  drafts,
  onOpenGallery,
  onOpenDraftsList,
  isDrawerOpen,
  setIsDrawerOpen,
  onOpenReel,
  setIsprofileReelOpen,
  storiesForDisplay,
  setOpenCreateStoryDialog,
  currentTime,
  isMuted,
  setOpenMyPost,
}: any) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        width: '100%',
        p: 2, // Reduced padding for a tighter fit
        height: 'calc(100vh - 60px)',
        overflowY: 'auto',
        bgcolor: 'background.default',
      }}
      role="presentation"
    >
      <ProfileSidebarComponent
        user={user}
        selectedProfileUser={selectedProfileUser}
        myPosts={myPosts}
        otherUserProfilePosts={otherUserProfilePosts}
        drafts={drafts}
        onOpenGallery={onOpenGallery}
        onOpenDraftsList={onOpenDraftsList}
        theme={theme}
        onOpenReel={onOpenReel}
        setIsprofileReelOpen={setIsprofileReelOpen}
        storiesForDisplay={storiesForDisplay}
        setOpenCreateStoryDialog={setOpenCreateStoryDialog}
        currentTime={currentTime}
        isMuted={isMuted}
        setOpenMyPost={setOpenMyPost}
      />
    </Box>
  );
};

export default ProfilePageWithDrawer;
