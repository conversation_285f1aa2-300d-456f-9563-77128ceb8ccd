import { createSlice } from '@reduxjs/toolkit';
import { OnlineVideoClassState } from '@/types/OnlineVideoClass';
import { fetchVideoClassList, fetchChapterFilterData } from './onlineVideoClass.thunks';
import { flushStore } from '../flush.slice';

const initialState: OnlineVideoClassState = {
  chapterFilterData: {
    data: [],
    status: 'idle',
    error: null,
  },
  videoClassList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  error: null,
};

export const onlineVideoClassSlice = createSlice({
  name: 'onlineVideoClass',
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder
      // Fetch Video Class List
      .addCase(fetchVideoClassList.pending, (state) => {
        state.videoClassList.status = 'loading';
        state.videoClassList.error = null;
      })
      .addCase(fetchVideoClassList.fulfilled, (state, action) => {
        state.videoClassList.status = 'success';
        state.videoClassList.data = action.payload;
        state.videoClassList.error = null;
      })
      .addCase(fetchVideoClassList.rejected, (state, action) => {
        state.videoClassList.status = 'error';
        state.videoClassList.error = (action.payload as string) || 'Unknown error in fetching video class list';
      })
      // Fetch Chapter Filter Data
      .addCase(fetchChapterFilterData.pending, (state) => {
        state.chapterFilterData.status = 'loading';
        state.chapterFilterData.error = null;
      })
      .addCase(fetchChapterFilterData.fulfilled, (state, action) => {
        state.chapterFilterData.status = 'success';
        state.chapterFilterData.data = action.payload;
        state.chapterFilterData.error = null;
      })
      .addCase(fetchChapterFilterData.rejected, (state, action) => {
        state.chapterFilterData.status = 'error';
        state.chapterFilterData.error = (action.payload as string) || 'Unknown error in fetching chapter filter data';
      })
      .addCase(flushStore, () => initialState);
  },
});

export default onlineVideoClassSlice.reducer;
