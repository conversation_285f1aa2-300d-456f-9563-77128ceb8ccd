import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Avatar,
  Box,
  Dialog,
  DialogContent,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography,
  Drawer,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Post, User, getInitials } from './Social';
import RoundedSearchField from './SearchFeildSocial';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  posts: Post[];
  users: User[];
  onOpenProfile?: (userName: string) => void;
  onSelectPost?: (postId: number) => void;
  setOpenSearch?: any;
}

const SearchDialog: React.FC<SearchDialogProps> = ({
  open,
  onClose,
  posts,
  users,
  onOpenProfile,
  onSelectPost,
  setOpenSearch,
}) => {
  const [query, setQuery] = useState('');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const inputRef = useRef<HTMLInputElement>(null);

  // Autofocus input when open changes
  useEffect(() => {
    if (!open) return;
    const delay = isMobile ? 300 : 120;
    const t = window.setTimeout(() => {
      inputRef.current?.focus();
      const el = inputRef.current;
      if (el && query.length) {
        const len = el.value.length;
        try {
          el.setSelectionRange(len, len);
        } catch {}
      }
    }, delay);
    return () => window.clearTimeout(t);
  }, [open, isMobile, query]);

  const normalized = (s: string) => s?.toLowerCase?.() || '';

  const matchedUsers = useMemo(() => {
    if (!query.trim()) return [];
    const q = normalized(query);
    return users.filter((u) => normalized(u.userName).includes(q) || normalized(u.className).includes(q));
  }, [users, query]);

  const matchedPosts = useMemo(() => {
    if (!query.trim()) return [];
    const q = normalized(query);
    return posts.filter(
      (p) =>
        normalized(p.userName).includes(q) ||
        normalized(p.caption || p.text || '').includes(q) ||
        (p.likedByUsers || []).some((u) => normalized(u.userName).includes(q))
    );
  }, [posts, query]);

  const handleUserClick = (u: User) => {
    onOpenProfile?.(u.userName);
    setOpenSearch(false);
  };

  const handlePostClick = (p: Post) => {
    if (onSelectPost) onSelectPost(p.id);
    else onOpenProfile?.(p.userName);
    setOpenSearch(false);
  };

  // ✨ Instagram-like layout
  const SearchContent = (
    <Box sx={{ px: 2, pt: 1 }}>
      {/* 🔍 Search Bar */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 2,
        }}
      >
        <RoundedSearchField
          inputRef={inputRef}
          value={query}
          onChange={(e: any) => setQuery(e.target.value)}
          placeholder="Search users, posts or tags"
          border={`1px solid ${theme.palette.grey[300]}`}
        />
        <IconButton
          onClick={() => {
            setQuery('');
            onClose();
          }}
          size="small"
          sx={{ ml: 1 }}
        >
          <CloseIcon />
        </IconButton>
      </Box>

      {/* 🧠 Show results only when searching */}
      {query.trim() ? (
        <>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            People
          </Typography>
          <List dense>
            {matchedUsers.length === 0 ? (
              <Typography color="text.secondary">No users found</Typography>
            ) : (
              matchedUsers.map((u) => (
                <ListItem key={u.userName} button onClick={() => handleUserClick(u)}>
                  <ListItemAvatar>
                    <Avatar src={u.avatar}>{getInitials(u.userName)}</Avatar>
                  </ListItemAvatar>
                  <ListItemText primary={u.userName} secondary={u.className} />
                </ListItem>
              ))
            )}
          </List>

          <Divider sx={{ my: 1 }} />

          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Posts
          </Typography>
          <List dense>
            {matchedPosts.length === 0 ? (
              <Typography color="text.secondary">No posts found</Typography>
            ) : (
              matchedPosts.map((p) => (
                <ListItem key={p.id} button onClick={() => handlePostClick(p)}>
                  <ListItemAvatar>
                    <Avatar src={p.avatar}>{getInitials(p.userName)}</Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={p.userName}
                    secondary={(p.caption || p.text || '').slice(0, 80) || 'View post'}
                  />
                </ListItem>
              ))
            )}
          </List>
        </>
      ) : (
        // 👇 Show nothing until user types (Instagram style)
        <Box
          sx={{
            textAlign: 'center',
            mt: 6,
            color: 'text.secondary',
          }}
        >
          <Typography variant="body2">Search for people or posts</Typography>
        </Box>
      )}
    </Box>
  );

  // 📱 Mobile Bottom Drawer
  if (isMobile) {
    return (
      <Drawer
        anchor="bottom"
        open={open}
        onClose={onClose}
        PaperProps={{
          sx: {
            height: '100vh',
            overflow: 'auto',
          },
        }}
      >
        {SearchContent}
      </Drawer>
    );
  }

  // 💻 Desktop Dialog
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogContent dividers>{SearchContent}</DialogContent>
    </Dialog>
  );
};

export default SearchDialog;
