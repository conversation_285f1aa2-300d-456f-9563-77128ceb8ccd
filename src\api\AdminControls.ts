import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import { AdminLoginDataType, AdminLoginRequest, ParentLoginDataType, ParentLoginRequest } from '@/types/AdminControls';

async function GetAdminLoginList(request: AdminLoginRequest): Promise<APIResponse<AdminLoginDataType[]>> {
  const response = await privateApi.post<AdminLoginDataType[]>(ApiUrls.GetAdminLoginList, request);
  console.log('response', response);
  return response;
}

async function GetParentLoginList(request: ParentLoginRequest): Promise<APIResponse<ParentLoginDataType[]>> {
  const response = await privateApi.post<ParentLoginDataType[]>(ApiUrls.GetParentLoginList, request);
  console.log('response', response);
  return response;
}

const methods = {
  GetAd<PERSON><PERSON><PERSON><PERSON><PERSON>ist,
  GetParentLoginList,
};

export default methods;
