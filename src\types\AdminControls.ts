import { FetchStatus } from './Common';

// Admin Login //
export type AdminLoginRequest = {
  staffName: string;
  loginName: string;
  status: number;
};

export type AdminLoginDataType = {
  adminId: number;
  staffId: number;
  loginName: string;
  password: string;
  role: string;
  status: number;
  staffName: string;
  mobileNo: string;
  zoomUserName: string;
  zoomPassword: string;
};

// Parent Login //
export type ParentLoginRequest = {
  adminId: number;
  academicId: number;
  classId: number;
  studentId: number;
  mobileNo: string;
};

export type ParentLoginDataType = {
  loginId: number;
  guardianName: string;
  userName: string;
  password: string;
  mobileNo: string;
  className: string;
  createDate: number;
  status: number;
};

// Admin Controls State //
export type AdminControlsState = {
  manageLoginList: {
    status: FetchStatus;
    data: AdminLoginDataType[];
    error: string | null;
  };
  parentLoginList: {
    status: FetchStatus;
    data: ParentLoginDataType[];
    error: string | null;
  };
  submitting: boolean;
  deletingRecords: Record<number, boolean>;
  error: string | null | undefined;
};
