import React, { useEffect } from 'react';
import { Box, Tabs, Tab, CircularProgress, Typography } from '@mui/material';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { LiveClassListData } from '@/types/LiveClass';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getLiveClassAttendReportData,
  getLiveClassAttendReportStatus,
  getLiveClassUnAttendReportData,
  getLiveClassUnAttendReportStatus,
} from '@/config/storeSelectors';
import { fetchLiveClassAttendReport, fetchLiveClassUnAttendReport } from '@/store/LiveClass/liveClass.thunks';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';
import attendanceMarked from '@/assets/statusCard/attendanceMarked.png';

// Attendance List Popup Content Component
const AttendanceListPopupContent: React.FC<{
  liveClass: LiveClassListData;
  tabValue: number;
  theme: any;
  onTabChange: (event: React.SyntheticEvent, newValue: number) => void;
}> = ({ liveClass, tabValue, onTabChange, theme }) => {
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId = user?.accountId || 0;

  // Get data from Redux store
  const presentStudents = useAppSelector(getLiveClassAttendReportData);
  const presentStatus = useAppSelector(getLiveClassAttendReportStatus);
  const absentStudents = useAppSelector(getLiveClassUnAttendReportData);
  const absentStatus = useAppSelector(getLiveClassUnAttendReportStatus);

  // Fetch attendance data when tab changes or component mounts
  useEffect(() => {
    if (tabValue === 0 && presentStatus === 'idle') {
      // Fetch present students
      dispatch(
        fetchLiveClassAttendReport({
          adminId,
          liveclassId: liveClass.liveClassId,
        })
      );
    } else if (tabValue === 1 && absentStatus === 'idle') {
      // Fetch absent students
      dispatch(
        fetchLiveClassUnAttendReport({
          adminId,
          academicId: liveClass.academicId,
          classId: liveClass.classId,
          liveclassId: liveClass.liveClassId,
        })
      );
    }
  }, [tabValue, dispatch, adminId, liveClass, presentStatus, absentStatus]);

  const unAttendedColumns: DataTableColumn<any>[] = [
    {
      name: 'className',
      dataKey: 'className',
      headerLabel: 'Class',
    },
    {
      name: 'studentName',
      dataKey: 'studentName',
      headerLabel: 'Student Name',
    },
    {
      name: 'fatherName',
      dataKey: 'fatherName',
      headerLabel: 'Parent',
    },
    {
      name: 'mobileNo',
      dataKey: 'mobileNo',
      headerLabel: 'Phone No',
    },
  ];

  const attendedColumns: DataTableColumn<any>[] = [
    // {
    //   name: 'rollNo',
    //   headerLabel: 'Roll No',
    //   renderCell: (index) => {
    //     return <Typography variant="subtitle2">{index + 1}</Typography>;
    //   },
    // },
    {
      name: 'className',
      dataKey: 'className',
      headerLabel: 'Class',
    },
    {
      name: 'studentName',
      dataKey: 'studentName',
      headerLabel: 'Student Name',
    },
    {
      name: 'zoomStartDate',
      headerLabel: 'Joined Time',
      renderCell: (row) => {
        return `${dayjs(row.zoomStartDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, hh:mm a')}`;
      },
    },
    {
      name: 'attentTotalTime',
      dataKey: 'attentTotalTime',
      headerLabel: 'Total Time',
    },
  ];
  return (
    <Box sx={{ mx: 3 }}>
      <Tabs
        value={tabValue}
        onChange={onTabChange}
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          mb: 2,
        }}
      >
        <Tab
          label={`Present (${liveClass.totalAttendStudent})`}
          sx={{
            textTransform: 'none',
            fontSize: 14,
            fontWeight: tabValue === 0 ? 600 : 400,
          }}
        />
        <Tab
          label={`Absent (${liveClass.totalStudent - liveClass.totalAttendStudent})`}
          sx={{
            textTransform: 'none',
            fontSize: 14,
            fontWeight: tabValue === 1 ? 600 : 400,
          }}
        />
      </Tabs>

      <Box
        sx={{
          border: `0.5px solid ${theme.palette.grey[300]}`,
          overflow: 'auto',
          '&::-webkit-scrollbar': { width: 0 },
        }}
        width="100%"
        height={{ xs: 'calc(100vh - 300px)', sm: 'calc(100vh - 300px)', lg: 'calc(100vh - 300px)' }}
      >
        {tabValue === 0 && presentStatus === 'loading' && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        )}
        {tabValue === 0 &&
          presentStatus !== 'loading' &&
          (presentStudents?.length === 0 ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <img src={attendanceMarked} width="120px" alt="" />
              <Typography variant="subtitle2" mt={3} fontSize={18} color="GrayText">
                No students attended the live class!
              </Typography>
            </Box>
          ) : (
            <DataTable
              data={presentStudents}
              columns={attendedColumns}
              getRowKey={(row) => row.studentId}
              fetchStatus={presentStatus === 'success' ? 'success' : 'error'}
            />
          ))}
        {tabValue === 1 && absentStatus === 'loading' && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        )}
        {tabValue === 1 &&
          absentStatus !== 'loading' &&
          (absentStudents?.length === 0 ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <img src={attendanceMarked} width="120px" alt="" />
              <Typography variant="subtitle2" mt={3} fontSize={18} color="GrayText">
                All students are present for the live class!
              </Typography>
            </Box>
          ) : (
            <DataTable
              data={absentStudents}
              columns={unAttendedColumns}
              getRowKey={(row) => row.studentId}
              fetchStatus={absentStatus === 'success' ? 'success' : 'error'}
            />
          ))}
      </Box>
    </Box>
  );
};

export default AttendanceListPopupContent;
