/* eslint-disable no-nested-ternary */
import React, { useC<PERSON>back, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
  Avatar,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/NirmalaLogo.png';

const A4Div = styled.div`
  width: 207mm;
  min-height: 297mm;
  border: 1.5px solid black;
  padding: 5mm;
  margin-bottom: 10mm;
  box-sizing: border-box;
  border-radius: 10px;
  page-break-before: always;
  page-break-after: always;
`;

type DummyDataType = {
  subject: string;
  termA: number | string;
  termB?: number | string | null;
};

const dummyData: DummyDataType[] = [
  {
    subject: 'ENGLISH WRITING',
    termA: 'A1',
    termB: 'A2',
  },
  {
    subject: 'ENG/READING',
    termA: 'A1',
    termB: 'B2',
  },
  {
    subject: 'RECITATION',
    termA: 'A2',
    termB: 'B1',
  },
  {
    subject: 'STORY',
    termA: 'A1',
    termB: 'B1',
  },
  {
    subject: 'G.K.',
    termA: 'A2',
    termB: 'A2',
  },
  {
    subject: 'CONVERSATION',
    termA: 'A1',
    termB: 'A2',
  },
  {
    subject: 'MATHS ORAL',
    termA: 'A2',
    termB: 'A1',
  },
  {
    subject: 'MATHS WRITING',
    termA: 'A1',
    termB: 'A1',
  },
  {
    subject: 'DRAWING',
    termA: 'A2',
    termB: 'B1',
  },
  {
    subject: 'Over all',
    termA: 'Grade : A1 Remark : Outstanding',
    termB: ' Grade : A2 Remark : Excellent',
  },
];

const Nirmala2Root = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function Nirmala2({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [divCount, setDivCount] = useState(1);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();
  // const getRandomNumber = () => Math.floor(Math.random() * 1000000);

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Student_Wise_Progress_Report_Nirmala_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          // height: 5px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTable-root {
        }
      }
    `,
  });

  const getRowKey = useCallback((row) => row.slNo, []);

  const progressListColumns = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subject',
      },
      {
        name: 'termA',
        dataKey: 'termA',
        headerLabel: 'Term A',
      },
      {
        name: 'termB',
        dataKey: 'termB',
        headerLabel: 'Term B',
      },
    ],
    []
  );

  return (
    <Page title="Student Wise">
      <Nirmala2Root>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={componentRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {Array.from({ length: divCount }).map(() => (
                  <A4Div>
                    <Box>
                      <Stack direction="row" justifyContent="center" alignItems="center" gap={4}>
                        <img src={holyLogo} width={97} alt="logo" />
                        <Stack direction="column" justifyContent="center" alignItems="center" gap={0.5}>
                          <Typography variant="h6" fontSize={14}>
                            Carmel Jyothi Education Society&apos;s
                          </Typography>
                          <Typography variant="h6" fontSize={22}>
                            NIRMALA CONVENT HIGH SCHOOL
                          </Typography>
                          <Typography variant="h5" fontSize={11}>
                            Nirmalwadi, Kharadi, Pune-411014. Maharashtra Govt. Recog. no. (Pri): VLS 1002/ (1000/2)
                          </Typography>
                          <Typography variant="h5" fontSize={11}>
                            dated 14/01/2003 Govt. Recog. no. (Sec): VLS-60733/D
                          </Typography>
                          <Typography variant="h4" fontSize={16} py={2} fontWeight={500}>
                            PROGRESS REPORT 2023-2024
                          </Typography>
                        </Stack>
                      </Stack>
                      <Divider sx={{ borderWidth: 2, backgroundColor: 'black' }} />
                      <Box
                        my={0.5}
                        sx={{
                          display: 'flex',
                          justifyContent: { xs: 'start', sm: 'space-between' },
                          px: { xs: 3 },
                          py: 1,
                        }}
                        flexWrap="wrap"
                      >
                        <Stack direction="column" gap={1}>
                          <Typography variant="h6" fontSize={14}>
                            Student Name : <span style={{ fontWeight: 'normal' }}> MUHAMMED ANSAR A</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Date of birth : <span style={{ fontWeight: 'normal' }}>: 05-04-2020</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Roll No. :<span style={{ fontWeight: 'normal' }}> 1</span>
                          </Typography>
                        </Stack>
                        <Stack direction="column" gap={1} alignItems="end">
                          <Typography variant="h6" fontSize={14}>
                            Class & Section: <span style={{ fontWeight: 'normal' }}> I-A</span>
                          </Typography>
                          <Typography variant="h6" fontSize={14}>
                            Admission No. : <span style={{ fontWeight: 'normal' }}> 5839</span>
                          </Typography>
                        </Stack>
                        <Avatar src="" sx={{ width: 80, height: 80 }} />
                      </Box>
                      <Divider sx={{ borderWidth: 2, backgroundColor: 'black', mb: 3 }} />
                    </Box>
                    <Paper>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              {progressListColumns.map((column) => (
                                <TableCell
                                  align="center"
                                  key={column.dataKey}
                                  sx={{ border: `1px solid ${theme.palette.secondary.light}` }}
                                >
                                  {column.headerLabel}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dummyData.map((row, index) => (
                              <TableRow key={getRowKey(row)}>
                                {progressListColumns.map((column) => (
                                  <TableCell
                                    key={column.dataKey}
                                    align="center"
                                    sx={{
                                      height: 10,
                                      fontWeight: 700,
                                      fontSize: row.subject === 'Total' ? 16 : 11,
                                      border: `1px solid ${theme.palette.secondary.light}`,
                                    }}
                                  >
                                    {row[column.dataKey as keyof DummyDataType]}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                            <TableRow>
                              <TableCell colSpan={3} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                            <TableRow>
                              <TableCell
                                colSpan={3}
                                sx={{
                                  height: 10,
                                  fontWeight: 700,
                                  fontSize: 14,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  py: 1,
                                }}
                              >
                                Teacher&apos;s Remarks :
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell colSpan={3} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                      <TableContainer>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <TableCell
                                sx={{
                                  height: 10,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontSize: 11,
                                  fontWeight: 700,
                                }}
                              >
                                Grade Range
                              </TableCell>
                              {[
                                'A1 : 100-91',
                                'A2 : 90-81',
                                'B1 : 80-71',
                                'B2 : 70-61',
                                'C1 : 60-51',
                                'C2 : 50-41',
                                'D : 40-32',
                                'Below-32 : E',
                              ].map((column) => (
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 10,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontSize: 11,
                                    fontWeight: 700,
                                  }}
                                >
                                  {column}
                                </TableCell>
                              ))}
                            </TableRow>
                            <TableRow>
                              <TableCell
                                sx={{
                                  height: 10,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontSize: 11,
                                  fontWeight: 700,
                                }}
                              >
                                Remark
                              </TableCell>
                              {[
                                'Outstanding',
                                'Excellent',
                                'Best',
                                'Very-Good',
                                'Good',
                                'Satisfactory',
                                'Mediocre',
                                'Need Improvement',
                              ].map((column) => (
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 10,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontSize: 11,
                                    fontWeight: 700,
                                  }}
                                >
                                  {column}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <Paper sx={{ pt: 5 }}>
                      <TableContainer>
                        <Typography variant="h6" align="center" fontSize={16} sx={{ py: 1 }}>
                          ATTENDANCE DETAILS
                        </Typography>
                        <Table>
                          <TableRow>
                            {[' No.of Working Days', 'No.of Present', 'Attendance Percentage'].map((row) => (
                              <TableCell
                                align="center"
                                sx={{
                                  height: 7,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontWeight: 600,
                                  fontSize: 13,
                                  color: 'Grey',
                                }}
                              >
                                {row}
                              </TableCell>
                            ))}
                          </TableRow>
                          <TableBody>
                            <TableRow>
                              {[160, 148, 93].map((column) => (
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 7,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontWeight: 500,
                                    fontSize: 13,
                                  }}
                                >
                                  {column}
                                </TableCell>
                              ))}
                            </TableRow>
                            <TableRow>
                              <TableCell
                                align="left"
                                colSpan={3}
                                sx={{
                                  height: 7,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontWeight: 700,
                                  fontSize: 13,
                                }}
                              >
                                Promotion status: Passed to Std II
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <Stack direction="row" gap={1} mt={10} pt={10} justifyContent="space-around">
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Date : 07-03-2025
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Signature of Teacher
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        Signature of Principal
                      </Typography>
                    </Stack>
                  </A4Div>
                ))}
              </Box>
            </Box>
          </div>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Box>
                <Button
                  variant="contained"
                  disabled={divCount === 0}
                  onClick={() => setDivCount(divCount - 1)}
                  sx={{ ml: 2 }}
                >
                  -
                </Button>
                <TextField
                  type="number"
                  value={divCount}
                  onChange={(e) => setDivCount(Number(e.target.value))}
                  sx={{ mx: 2 }}
                />
                <Button variant="contained" onClick={() => setDivCount(divCount + 1)}>
                  +
                </Button>
                <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                  Print
                </Button>
              </Box>
            </Stack>
          </Box>
        </Card>
      </Nirmala2Root>
    </Page>
  );
}

export default Nirmala2;
