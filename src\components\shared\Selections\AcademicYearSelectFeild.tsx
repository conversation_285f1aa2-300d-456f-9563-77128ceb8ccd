/* eslint-disable no-nested-ternary */
import React, { useEffect } from 'react';
import { FormControl, MenuItem, Select, SelectChangeEvent, Typography, Skeleton } from '@mui/material';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getYearData, getYearStatus } from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';

interface AcademicYearSelectFieldProps {
  value: number | undefined;
  onChange: (newValue: number) => void;
  label?: string;
  sx?: object;
}

const AcademicYearSelectField: React.FC<AcademicYearSelectFieldProps> = ({
  value,
  onChange,
  label = 'Academic Year',
  sx = {},
}) => {
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId = user?.accountId || 0;
  const yearData = useAppSelector(getYearData);
  const yearStatus = useAppSelector(getYearStatus); // 'idle' | 'loading' | 'succeeded' | 'failed'

  // Fetch year list on mount if not available
  useEffect(() => {
    if (yearStatus === 'idle') {
      dispatch(fetchYearList(adminId));
    }
  }, [dispatch, adminId, yearStatus]);

  // Automatically select the latest year if no value is set
  useEffect(() => {
    if (yearData?.length > 0 && !value) {
      const sorted = [...yearData].sort((a, b) => b.accademicTime.localeCompare(a.accademicTime));
      const latestYear = sorted[0];
      if (latestYear) {
        onChange(latestYear.accademicId);
      }
    }
  }, [yearData, value, onChange]);

  const handleChange = (event: SelectChangeEvent<number>) => {
    const selectedValue = Number(event.target.value);
    onChange(selectedValue);
  };

  return (
    <FormControl fullWidth sx={{ ...sx }}>
      <Typography variant="subtitle1" fontSize={12} color="GrayText">
        {label}
      </Typography>

      {/* If loading, show skeleton for smoother UX */}
      {yearStatus === 'loading' ? (
        <Skeleton variant="rounded" height={38} width="100%" animation="wave" />
      ) : (
        <Select value={value || ''} onChange={handleChange} displayEmpty sx={{ minWidth: 120 }}>
          {yearStatus === 'idle' ? (
            <MenuItem disabled>Loading...</MenuItem>
          ) : yearData?.length > 0 ? (
            yearData.map((year) => (
              <MenuItem key={year.accademicId} value={year.accademicId}>
                {year.accademicTime}
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>No Data</MenuItem>
          )}
        </Select>
      )}
    </FormControl>
  );
};

export default AcademicYearSelectField;
