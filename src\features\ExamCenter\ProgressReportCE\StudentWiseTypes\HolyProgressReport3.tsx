import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/StThereseLogo.png';
import Page from '@/components/shared/Page';
import logo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import user from '@/assets/user.jpg';
import img from '@/assets/holyProgressCardImg/img.png';
import img1 from '@/assets/holyProgressCardImg/img1.png';
import img2 from '@/assets/holyProgressCardImg/img2.png';
import img3 from '@/assets/holyProgressCardImg/img3.png';
import img4 from '@/assets/holyProgressCardImg/img4.png';
import img5 from '@/assets/holyProgressCardImg/img5.png';
import img6 from '@/assets/holyProgressCardImg/img6.png';
import img7 from '@/assets/holyProgressCardImg/img7.png';
import img8 from '@/assets/holyProgressCardImg/img8.png';
import img9 from '@/assets/holyProgressCardImg/img9.png';
import img10 from '@/assets/holyProgressCardImg/img10.png';
import img11 from '@/assets/holyProgressCardImg/img11.png';
import img12 from '@/assets/holyProgressCardImg/img12.png';
import img13 from '@/assets/holyProgressCardImg/img13.png';
import img14 from '@/assets/holyProgressCardImg/img14.png';
import img15 from '@/assets/holyProgressCardImg/img15.png';
import img16 from '@/assets/holyProgressCardImg/img16.png';
import img17 from '@/assets/holyProgressCardImg/img17.png';
import img18 from '@/assets/holyProgressCardImg/img18.png';
import img19 from '@/assets/holyProgressCardImg/img19.png';
import img20 from '@/assets/holyProgressCardImg/img20.png';
import img21 from '@/assets/holyProgressCardImg/img21.png';
import img22 from '@/assets/holyProgressCardImg/img22.png';
import img23 from '@/assets/holyProgressCardImg/img23.png';
import img24 from '@/assets/holyProgressCardImg/img24.png';
import img25 from '@/assets/holyProgressCardImg/img25.png';
import img26 from '@/assets/holyProgressCardImg/img26.png';
import img27 from '@/assets/holyProgressCardImg/img27.png';
import img28 from '@/assets/holyProgressCardImg/img28.png';
import img29 from '@/assets/holyProgressCardImg/img29.png';

const A4Div = styled.div`
  page-break-before: always;
  page-break-after: always;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  border: '1px solid black',
  height: '24px',
  fontSize: 11,
  fontWeight: 600,
  textAlign: 'center', // Aligns text to the center
  '&.table-cell': {
    // Additional styles for the "table-cell" class if needed
  },
}));

type StuddentWiseProgrssReport = {
  subject: string;
  semester1: string | null;
  semester2: string | null;
};

type AttendanceData = {
  month: string;
  workingDays: number | null;
  presentDays: number | null;
};

const dummyData: StuddentWiseProgrssReport[] = [
  { subject: 'TOTAL', semester1: null, semester2: null },
  { subject: 'ALGEBRA', semester1: 'E', semester2: null },
  { subject: 'GEOMETRY', semester1: 'C2', semester2: null },
  { subject: 'TOTAL (MATHS)', semester1: null, semester2: null },
  { subject: 'SCIENCE 1', semester1: 'E', semester2: null },
  { subject: 'SCIENCE 2', semester1: 'D', semester2: null },
  { subject: 'TOTAL (SCIENCE)', semester1: null, semester2: null },
  { subject: 'HISTORY & POLITICAL SC.', semester1: 'D', semester2: null },
  { subject: 'GEOGRAPHY', semester1: 'C1', semester2: null },
  { subject: 'TOTAL (SS)', semester1: null, semester2: null },
];

const attendanceData: AttendanceData[] = [
  { month: 'JUNE', workingDays: null, presentDays: null },
  { month: 'JULY', workingDays: null, presentDays: null },
  { month: 'AUG', workingDays: null, presentDays: null },
  { month: 'SEP', workingDays: null, presentDays: null },
  { month: 'OCT', workingDays: null, presentDays: null },
  { month: 'NOV', workingDays: null, presentDays: null },
  { month: 'DEC', workingDays: null, presentDays: null },
  { month: 'JAN', workingDays: null, presentDays: null },
  { month: 'FEB', workingDays: null, presentDays: null },
  { month: 'MAR', workingDays: null, presentDays: null },
  { month: 'APR', workingDays: null, presentDays: null },
];

const Holy3Root = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function Holy3({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [reportCount, setReportCount] = useState(1);
  const reportRef = useRef<(HTMLDivElement | null)[]>([]);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();
  // const getRandomNumber = () => Math.floor(Math.random() * 1000000);

  const handlePrint = useReactToPrint({
    content: () => {
      const printableContent = document.createElement('div');
      reportRef.current.forEach((ref) => {
        if (ref) {
          printableContent.appendChild(ref.cloneNode(true));
        }
      });
      return printableContent;
    },
  });

  const progressListColumns = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subjects',
      },
      {
        name: 'semester1',
        dataKey: 'semester1',
        headerLabel: 'Semester 1',
      },
      {
        name: 'semester2',
        dataKey: 'semester2',
        headerLabel: 'Semester 2',
      },
    ],
    []
  );

  const attendanceColumns = useMemo(
    () => [
      {
        name: 'month',
        dataKey: 'month',
        headerLabel: 'Month',
      },
      {
        name: 'workingDays',
        dataKey: 'workingDays',
        headerLabel: 'No. of Working Days',
      },
      {
        name: 'presentDays',
        dataKey: 'presentDays',
        headerLabel: 'No. of Days Present',
      },
    ],
    []
  );

  return (
    <Page title="Student Wise">
      <Holy3Root>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Stack direction="row" spacing={2} mt={20} mb={2}>
              <Button variant="contained" onClick={() => setReportCount((prev) => Math.max(1, prev - 1))} color="error">
                -
              </Button>
              <Typography variant="h6">Pages: {reportCount}</Typography>
              <Button variant="contained" onClick={() => setReportCount((prev) => prev + 1)} color="success">
                +
              </Button>
              <Button variant="contained" color="primary" onClick={handlePrint}>
                Print Report Card
              </Button>
            </Stack>

            {/* ReportCard List */}
            <A4Div>
              {Array.from({ length: reportCount }).map((_, index) => {
                return (
                  <Box
                    ref={(el: HTMLDivElement | null) => {
                      if (reportRef.current && el) {
                        reportRef.current[index] = el;
                      }
                    }}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      '@media print': {
                        padding: 0,
                      },
                    }}
                  >
                    {/* =============== Page No 1 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm', // Adjusted width for margin
                        height: '302.5mm', // Adjusted height for margin
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                      }}
                    >
                      <Stack mt={5} direction="row" alignItems="center" gap={2} justifyContent="center">
                        <img src={logo} width={100} alt="" />
                        <Typography variant="h4" color="error" fontWeight="bold">
                          <i> ANGELS&apos; PARADISE</i>
                        </Typography>
                      </Stack>
                      <Stack direction="column" alignItems="center" gap={2} justifyContent="center">
                        <Typography color="black" variant="h6" fontStyle="italic">
                          REPORT CARD
                        </Typography>
                        <Typography color="black" variant="h6" fontStyle="italic">
                          (ACADEMIC YEAR 2024-2025)
                        </Typography>
                      </Stack>
                      <Grid container justifyContent="center" style={{ margin: '10px 0 30px 0 ' }}>
                        <Avatar
                          src={user}
                          sx={{ borderRadius: 0, width: '3.5cm', height: '4.5cm', border: '2px solid black' }}
                        />
                      </Grid>
                      {[
                        ['NAME', 'AADIRAJ SAGAR MORE'],
                        ['ROLL NO.', '1'],
                        ["FATHER'S NAME", 'SAGAR'],
                        ["MOTHER'S NAME", 'ASHWINI'],
                        ['CLASS', 'JRKG'],
                        ['SECTION', 'A'],
                        ['DATE OF BIRTH', '07/Apr/2020'],
                        ['NAME OF THE CLASS TEACHER', 'JANHAVI SHRIPURN JOSHI'],
                      ].map(([label, value], index) => (
                        <Stack
                          key={index}
                          direction="row"
                          alignItems="center"
                          gap={0}
                          justifyContent="space-around"
                          pl={5}
                        >
                          <Stack width="40%" textAlign="start" mb={4}>
                            <Typography color="black" variant="subtitle2" fontSize={13}>
                              {label}
                            </Typography>
                          </Stack>
                          <Stack width="60%" textAlign="start" mb={4}>
                            <Typography color="black" variant="subtitle2" fontSize={13}>
                              :&nbsp;{value}
                            </Typography>
                          </Stack>
                        </Stack>
                      ))}
                      <Stack mt={0} direction="row" justifyContent="space-between" alignItems="center">
                        <div>
                          <img width={150} src={img} alt="img1" />
                        </div>
                        <div>
                          <img width={190} src={img1} alt="img1" />
                        </div>
                      </Stack>
                    </Paper>

                    {/* Page Break - Forces Next Content to Print on a New A4 Page */}
                    {/* =============== Page No 2 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm',
                        height: '302.5mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      {' '}
                    </Paper>
                    {/* =============== Page No 3 =============== */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm',
                        height: '302.5mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                HEALTH STATUS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={3}
                              >
                                <Stack alignItems="center">
                                  <img width={60} src={img2} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={3}
                              >
                                <Stack alignItems="center">
                                  <img width={60} src={img3} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>HEIGHT</StyledTableCell>
                              <StyledTableCell>105 cm</StyledTableCell>
                              <StyledTableCell>108 cm</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WEIGHT</StyledTableCell>
                              <StyledTableCell>16 kg</StyledTableCell>
                              <StyledTableCell>16 kg</StyledTableCell>
                            </TableRow>
                            {/* LARGE MOTOR SKILLS */}
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                LARGE MOTOR SKILLS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={11}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={100} src={img4} alt="" />
                                  <img width={100} src={img5} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={11}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={100} src={img6} alt="" />
                                  <img width={100} src={img7} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WALKING IN STRAIGHT LINE</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WALKING ON ZIG-ZAG LINE</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RUNNING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>JUMPING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>KICKING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>THROWING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>CATCHING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>BALANCING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>HOPPING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>DANCING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            {/* FINE MOTOR SKILLS */}
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                FINE MOTOR SKILLS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={15}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={100} src={img8} alt="" />
                                  <img width={100} src={img9} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={15}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={130} src={img10} alt="" />
                                  <img width={130} src={img11} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>PASSING THE PARCEL</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>PICKING OBJECT</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>ZIPPING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>BUTTONING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>BUCKLING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>MOULDING CLAY</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>PAPER FOLDING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>LACING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>BEADING</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>COMPLETES PUZZLES</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>TEARING & PASTING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>HOLDING</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>TURNING THE PAGES</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>PINCER GRIP</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>

                    {/* =============== Page No 4 =============== */}

                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm',
                        height: '302.5mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                PERSONALITY DEVELOPMENT
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={12}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={100} src={img12} alt="" />
                                  <img width={120} src={img13} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={12}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={120} src={img14} alt="" />
                                  <img width={120} src={img15} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>SHARES THINGS</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>COMES NEATLY DRESSED</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>FOLLOWS PERSONAL HYGIENE</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>REGULAR TO SCHOOL</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>COMES ON TIME</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RESPECTS TEACHER</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RESPECTS PEER GROUP</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>COMPLETES TASK ON TIME</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WAITS FOR TURN</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>PARTICIPATES IN ACTIVITY</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WORKS INDEPENDENTLY</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            {/*  LITERACY SKILLS */}
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                LITERACY SKILLS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={14}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={130} src={img16} alt="" />
                                  <img width={130} src={img17} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={14}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={130} src={img18} alt="" />
                                  <img width={130} src={img19} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>LISTENING TO RHYME</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>LISTENING TO STORIES</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RECOGNISES LETTERS</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>IDENTIFIES PHONIC SOUND</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RECITES RHYME</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RECITES PHONIC SONG WITH ACTION</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>READS WORDS WITH PHONIC SOUND</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RECOGNISES SIGHT WORDS</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>CONVERSE IN ENGLISH</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>NARRATES STORY</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>TRACE/WRITE LETTERS OR SENTENCES</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>NEATNESS IN WRITING</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WRITES WITH CORRECT FORMATION</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                            </TableRow>
                            {/* FINE MOTOR SKILLS */}
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                colSpan={5}
                                align="center"
                              >
                                NUMERACY SKILLS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={15}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={120} src={img20} alt="" />
                                  <img width={120} src={img21} alt="" />
                                </Stack>
                              </StyledTableCell>
                              <StyledTableCell> </StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                              <StyledTableCell
                                style={{ border: '1px solid black', height: '24px', fontSize: 11, fontWeight: 600 }}
                                className="table-cell"
                                align="center"
                                rowSpan={15}
                              >
                                <Stack gap={2} alignItems="center">
                                  <img width={120} src={img22} alt="" />
                                  <img width={120} src={img23} alt="" />
                                </Stack>
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RECOGNISES NUMBERS</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>COUNTS THE OBJECTS</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>SEQUENCING THE NUMBERS</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>AWARE OF MATHEMATICAL CONCEPT</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>TRACE/WRITE NUMBERS</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WRITES WITH CORRECT FORMATION</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>WRITES WITH CORRECT FORMATION</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>NEATNESS IN WRITING</StyledTableCell>
                              <StyledTableCell>Need Practice</StyledTableCell>
                              <StyledTableCell>Sometime</StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>EXPLORE WAYS TO SOLVE PROBLEM</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                              <StyledTableCell>Always</StyledTableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>
                    {/* Page No 5 */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm',
                        height: '302.5mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* GENERAL AWARENESS SKILLS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                GENERAL AWARENESS SKILLS
                              </StyledTableCell>
                            </TableRow>
                            {generalAwarenessSkillsData.map((row, index) => (
                              <TableRow key={index}>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={generalAwarenessSkillsData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img24} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                                <StyledTableCell>{row.label}</StyledTableCell>
                                <StyledTableCell>{row.term1}</StyledTableCell>
                                <StyledTableCell>{row.term2}</StyledTableCell>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={generalAwarenessSkillsData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img25} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                              </TableRow>
                            ))}

                            {/* MUSIC & MOVEMENTS */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                MUSIC & MOVEMENTS
                              </StyledTableCell>
                            </TableRow>
                            {musicAndMovementsData.map((row, index) => (
                              <TableRow key={index}>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={musicAndMovementsData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img26} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                                <StyledTableCell>{row.label}</StyledTableCell>
                                <StyledTableCell>{row.term1}</StyledTableCell>
                                <StyledTableCell>{row.term2}</StyledTableCell>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={musicAndMovementsData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img27} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                              </TableRow>
                            ))}

                            {/* ART & CRAFT */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ART & CRAFT
                              </StyledTableCell>
                            </TableRow>
                            {artAndCraftData.map((row, index) => (
                              <TableRow key={index}>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={artAndCraftData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img28} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                                <StyledTableCell>{row.label}</StyledTableCell>
                                <StyledTableCell>{row.term1}</StyledTableCell>
                                <StyledTableCell>{row.term2}</StyledTableCell>
                                {index === 0 && (
                                  <StyledTableCell rowSpan={artAndCraftData.length + 1} align="center">
                                    <Stack gap={2} alignItems="center">
                                      <img width={130} src={img29} alt="" />
                                    </Stack>
                                  </StyledTableCell>
                                )}
                              </TableRow>
                            ))}

                            {/* ACADEMIC ASSESSMENT */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ACADEMIC ASSESSMENT
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>Subject</StyledTableCell>
                              <StyledTableCell>Skill</StyledTableCell>
                              <StyledTableCell>Marks</StyledTableCell>
                              <StyledTableCell>TERM 1</StyledTableCell>
                              <StyledTableCell>TERM 2</StyledTableCell>
                            </TableRow>
                            {academicAssessmentData.map((subject, subjectIndex) =>
                              subject.skills.map((skill, skillIndex) => (
                                <TableRow key={`${subjectIndex}-${skillIndex}`}>
                                  {skillIndex === 0 && (
                                    <StyledTableCell rowSpan={subject.skills.length} align="center">
                                      {subject.subject}
                                    </StyledTableCell>
                                  )}
                                  <StyledTableCell>{skill.skill}</StyledTableCell>
                                  <StyledTableCell>{skill.marks}</StyledTableCell>
                                  <StyledTableCell>{skill.term1}</StyledTableCell>
                                  <StyledTableCell>{skill.term2}</StyledTableCell>
                                </TableRow>
                              ))
                            )}

                            {/* ATTENDANCE */}
                            <TableRow>
                              <StyledTableCell colSpan={5} align="center" style={{ fontWeight: 600 }}>
                                ATTENDANCE
                              </StyledTableCell>
                            </TableRow>
                            {attendanceData.map((row, index) => (
                              <TableRow key={index}>
                                <StyledTableCell colSpan={2} align="center">
                                  {row.label}
                                </StyledTableCell>
                                <StyledTableCell>{row.term1}</StyledTableCell>
                                <StyledTableCell>{row.term2}</StyledTableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Box>
                    </Paper>

                    {/* Page No 6 */}
                    <Paper
                      style={{
                        pageBreakAfter: 'always',
                        width: '210mm',
                        height: '302.5mm',
                        padding: '10px',
                        backgroundColor: '#ffff99',
                        border: '2px dashed black',
                        margin: 'auto',
                      }}
                    >
                      <Box border={1} borderColor="black">
                        <Stack direction="row" py={1} px={1} gap={1}>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            NAME:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={250}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            ROL NO:
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12} borderBottom={1} width={110}>
                            {' '}
                          </Typography>
                        </Stack>
                        <Table>
                          <TableBody>
                            {/* GRADATION */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                GRADATION
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell>RANGE</StyledTableCell>
                              <StyledTableCell>GRADE</StyledTableCell>
                              <StyledTableCell>PROFICIENCY LEVEL</StyledTableCell>
                            </TableRow>
                            {gradationData.map((row, index) => (
                              <TableRow key={index}>
                                <StyledTableCell>{row.range}</StyledTableCell>
                                <StyledTableCell>{row.grade}</StyledTableCell>
                                <StyledTableCell>{row.proficiency}</StyledTableCell>
                              </TableRow>
                            ))}

                            {/* TERM RESULT */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                TERM RESULT
                              </StyledTableCell>
                            </TableRow>
                            {termResultData.map((row, index) => (
                              <TableRow key={index}>
                                <StyledTableCell colSpan={2} align="center" style={{ fontWeight: 600 }}>
                                  {row.label}
                                </StyledTableCell>
                                <StyledTableCell>{row.value}</StyledTableCell>
                              </TableRow>
                            ))}

                            {/* REMARKS */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                REMARKS
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600, height: 100 }}>
                                Good
                              </StyledTableCell>
                            </TableRow>

                            {/* RESULT */}
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600 }}>
                                RESULT
                              </StyledTableCell>
                            </TableRow>
                            <TableRow>
                              <StyledTableCell colSpan={3} align="center" style={{ fontWeight: 600, height: 100 }}>
                                PASSED TO / PROMOTED TO CLASS : SRKG - C
                              </StyledTableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Typography color="black" p={1} variant="subtitle2" textAlign="start" fontSize={12}>
                          ISSUE DATE:
                        </Typography>
                        <Stack px={1} mt={20} direction="row" justifyContent="space-between">
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            CLASS TEACHER
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            PRINCIPAL
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            HEADMISTRESS
                          </Typography>
                          <Typography color="black" variant="subtitle2" fontSize={12}>
                            PARENT
                          </Typography>
                        </Stack>
                      </Box>
                    </Paper>
                  </Box>
                );
              })}
            </A4Div>
          </div>
        </Card>
      </Holy3Root>
    </Page>
  );
}

export default Holy3;
