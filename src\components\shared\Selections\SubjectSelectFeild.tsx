/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useCallback } from 'react';
import { FormControl, Select, MenuItem, Typography, SelectChangeEvent } from '@mui/material';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';

import {
  getCTSSubjectList,
  getCTSFilterListStatus,
  getCTSDetailsListStatus,
  getCTSDetailsListData,
} from '@/config/storeSelectors';

import { fetchCTSFilter, fetchCTSList } from '@/store/StaffMangement/staffMangement.thunks';

interface SubjectSelectFeildProps {
  adminId: number;
  currentRequest: any;
  subjectFilter: number;
  setSubjectFilter: (id: number) => void;
  onChange: (newValue: any) => void;
}

const SubjectSelectFeild: React.FC<SubjectSelectFeildProps> = ({
  adminId,
  currentRequest,
  subjectFilter,
  setSubjectFilter,
  onChange,
}) => {
  const dispatch = useAppDispatch();

  const SubjectList = useAppSelector(getCTSSubjectList);
  const FilterStatus = useAppSelector(getCTSFilterListStatus);

  /** Load subject list initially */
  useEffect(() => {
    if (FilterStatus === 'idle') {
      dispatch(fetchCTSFilter(adminId));
    }
  }, [FilterStatus]);

  /** Handle Subject Change */
  const handleSubjectChange = useCallback(
    (e: SelectChangeEvent) => {
      const selectedSubject = parseInt(e.target.value, 10);
      setSubjectFilter(selectedSubject);

      dispatch(
        fetchCTSList({
          ...currentRequest,
          subjectId: selectedSubject || -1,
        })
      );
    },
    [currentRequest]
  );

  return (
    <FormControl fullWidth size="small">
      <Select
        value={subjectFilter.toString()}
        onChange={onChange}
        displayEmpty
        MenuProps={{
          PaperProps: {
            style: {
              maxHeight: '250px', // Adjust the value to your desired height
            },
          },
        }}
      >
        <MenuItem value={-1}>All Subject</MenuItem>

        {SubjectList?.map((sub: any) => (
          <MenuItem key={sub.subjectId} value={sub.subjectId}>
            {sub.subjectName}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default SubjectSelectFeild;
