import React, { useEffect, useMemo } from 'react';
import { FormControl, MenuItem, Select, SelectChangeEvent, Typography, useTheme } from '@mui/material';
import { useAppSelector } from '@/hooks/useAppSelector';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { getClassData, getClassStatus } from '@/config/storeSelectors';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useAuth from '@/hooks/useAuth';

interface ClassSelectFeildProps {
  value: string | number;
  onChange: (event: SelectChangeEvent<string | number>) => void;
  includeAllOption?: boolean;
  label?: string;
  disabled?: boolean;
  sx?: object;
}

/**
 * ✅ Class Select component
 * - Fetches class list from Redux on mount
 * - Always defaults to "All Class" on initial load
 * - Reduced dropdown height
 */
const ClassSelectFeild: React.FC<ClassSelectFeildProps> = ({
  value = -1,
  onChange,
  includeAllOption = true,
  label = 'Class',
  disabled = false,
  sx = {},
}) => {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const adminId = user?.accountId ?? 0;

  const classData = useAppSelector(getClassData);
  const classStatus = useAppSelector(getClassStatus);

  // Fetch class list if not loaded
  useEffect(() => {
    if (classStatus === 'idle') {
      dispatch(fetchClassList(adminId));
    }
  }, [dispatch, adminId, classStatus]);

  // Automatically select "All Class" on initial load if not set
  useEffect(() => {
    if (!value) {
      const fakeEvent = {
        target: { value: -1 },
      } as unknown as SelectChangeEvent<string | number>;
      onChange(fakeEvent);
    }
  }, [value, onChange]);

  const classOptions = useMemo(() => {
    const base = includeAllOption ? [{ classId: -1, className: 'All Class' }, ...classData] : classData;
    return base;
  }, [classData, includeAllOption]);

  return (
    <FormControl fullWidth sx={{ ...sx }}>
      <Typography variant="subtitle1" fontSize={12} color="GrayText">
        {label}
      </Typography>

      <Select
        value={value || -1}
        onChange={onChange}
        size="small"
        displayEmpty
        disabled={disabled}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 270, // 👈 reduced dropdown height
              //   mt: 0.5,
              '& .MuiMenuItem-root': {
                px: 1.5,
              },
            },
          },
        }}
        sx={{
          //   fontSize: 13,
          ...sx,
        }}
      >
        {classOptions.map((cls) => (
          <MenuItem key={cls.classId} value={cls.classId}>
            {cls.className}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default ClassSelectFeild;
