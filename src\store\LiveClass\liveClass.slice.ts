import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  LiveClassAttendReportData,
  LiveClassListData,
  LiveClassState,
  LiveClassUnAttendReportData,
} from '@/types/LiveClass';
import {
  deleteZoomLiveClass,
  endZoomLiveClass,
  fetchLiveClassAttendReport,
  fetchLiveClassList,
  fetchLiveClassUnAttendReport,
} from './liveClass.thunks';
import { flushStore } from '../flush.slice';

const initialState: LiveClassState = {
  liveClassList: {
    data: [],
    status: 'idle',
    error: null,
  },
  liveClassAttendReport: {
    data: [],
    status: 'idle',
    error: null,
  },
  liveClassUnAttendReport: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const liveClassSlice = createSlice({
  name: 'liveClass',
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder
      // extraReducers for fetching Live Class List
      // ------Get------- //
      .addCase(fetchLiveClassList.pending, (state) => {
        state.liveClassList.status = 'loading';
        state.liveClassList.error = null;
      })
      .addCase(fetchLiveClassList.fulfilled, (state, action: PayloadAction<LiveClassListData[]>) => {
        state.liveClassList.status = 'success';
        state.liveClassList.data = action.payload;
        state.liveClassList.error = null;
      })
      .addCase(fetchLiveClassList.rejected, (state, action) => {
        state.liveClassList.status = 'error';
        state.liveClassList.error = (action.payload as string) || 'Unknown error in fetching Live Class List';
      })
      // extraReducers for End Zoom Live Class
      // ------Get------- //
      .addCase(endZoomLiveClass.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(endZoomLiveClass.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(endZoomLiveClass.rejected, (state, action) => {
        state.submitting = false;
        state.error = (action.payload as string) || 'Unknown error in ending Zoom Live Class';
      })
      // extraReducers for Delete Zoom Live Class
      // ------Get------- //
      .addCase(deleteZoomLiveClass.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(deleteZoomLiveClass.fulfilled, (state) => {
        state.submitting = false;
        state.error = null;
      })
      .addCase(deleteZoomLiveClass.rejected, (state, action) => {
        state.submitting = false;
        state.error = (action.payload as string) || 'Unknown error in deleting Zoom Live Class';
      })
      // extraReducers for fetching Live Class Attend Report
      // ------Get------- //
      .addCase(fetchLiveClassAttendReport.pending, (state) => {
        state.liveClassAttendReport.status = 'loading';
        state.liveClassAttendReport.error = null;
      })
      .addCase(fetchLiveClassAttendReport.fulfilled, (state, action: PayloadAction<LiveClassAttendReportData[]>) => {
        state.liveClassAttendReport.status = 'success';
        state.liveClassAttendReport.data = action.payload;
        state.liveClassAttendReport.error = null;
      })
      .addCase(fetchLiveClassAttendReport.rejected, (state, action) => {
        state.liveClassAttendReport.status = 'error';
        state.liveClassAttendReport.error =
          (action.payload as string) || 'Unknown error in fetching Live Class Attend Report';
      })
      // extraReducers for fetching Live Class Un Attend Report
      // ------Get------- //
      .addCase(fetchLiveClassUnAttendReport.pending, (state) => {
        state.liveClassUnAttendReport.status = 'loading';
        state.liveClassUnAttendReport.error = null;
      })
      .addCase(
        fetchLiveClassUnAttendReport.fulfilled,
        (state, action: PayloadAction<LiveClassUnAttendReportData[]>) => {
          state.liveClassUnAttendReport.status = 'success';
          state.liveClassUnAttendReport.data = action.payload;
          state.liveClassUnAttendReport.error = null;
        }
      )
      .addCase(fetchLiveClassUnAttendReport.rejected, (state, action) => {
        state.liveClassUnAttendReport.status = 'error';
        state.liveClassUnAttendReport.error =
          (action.payload as string) || 'Unknown error in fetching Live Class UnAttend Report';
      })
      .addCase(flushStore, () => initialState);
  },
});

export default liveClassSlice.reducer;
