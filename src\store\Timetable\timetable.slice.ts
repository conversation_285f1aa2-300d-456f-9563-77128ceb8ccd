import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { TimetableViewState } from '@/types/Timetable';
import { fetchTimetableViewByClass } from './timetable.thunks';
import { flushStore } from '../flush.slice';

const initialState: TimetableViewState = {
  timetableData: {
    status: 'idle',
    data: null,
    error: null,
  },
  submitting: false,
  error: null,
};

export const timetableSlice = createSlice({
  name: 'timetable',
  initialState,
  reducers: {
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchTimetableViewByClass.pending, (state) => {
        state.timetableData.status = 'loading';
        state.timetableData.error = null;
      })
      .addCase(fetchTimetableViewByClass.fulfilled, (state, action) => {
        state.timetableData.status = 'success';
        state.timetableData.data = action.payload;
        state.timetableData.error = null;
      })
      .addCase(fetchTimetableViewByClass.rejected, (state, action) => {
        state.timetableData.status = 'error';
        state.timetableData.error = action.payload || 'Unknown error in fetching timetable';
      })
      .addCase(flushStore, () => initialState);
  },
});

export const { setSubmitting, clearError } = timetableSlice.actions;

export default timetableSlice.reducer;

