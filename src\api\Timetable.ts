import ApiUrls from '@/config/ApiUrls';
import { privateApi } from '@/api/base/api';
import { APIResponse } from '@/api/base/types';
import { SubmitResponse } from '@/types/Common';
import {
  CheckStaffAvailabilityRequest,
  InsertTimetableRequest,
  TimetableEntryRequest,
  TimetableViewResponse,
} from '@/types/Timetable';

async function CheckStaffAvailability(request: CheckStaffAvailabilityRequest): Promise<APIResponse<SubmitResponse>> {
  const response = await privateApi.post<SubmitResponse>(ApiUrls.CheckStaffAvailability, request);
  return response;
}

async function InsertTimetable(request: InsertTimetableRequest): Promise<APIResponse<TimetableEntryRequest[]>> {
  const response = await privateApi.post<TimetableEntryRequest[]>(ApiUrls.InsertTimetable, request);
  return response;
}

async function GetTimetableViewByClass(
  adminId: number | undefined,
  academicId: number | undefined,
  classId: number | undefined
): Promise<APIResponse<TimetableViewResponse>> {
  if (adminId === undefined || academicId === undefined || classId === undefined) {
    throw new Error('adminId, academicId, and classId are required');
  }

  const url = ApiUrls.GetTimetableViewByClass.replace('{adminId}', adminId.toString())
    .replace('{academicId}', academicId.toString())
    .replace('{classId}', classId.toString());

  const response = await privateApi.get<TimetableViewResponse>(url);
  return response;
}

const methods = {
  CheckStaffAvailability,
  InsertTimetable,
  GetTimetableViewByClass,
};

export default methods;
