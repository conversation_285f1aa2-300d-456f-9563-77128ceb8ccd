import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Button,
  Divider,
  FormControl,
  Card,
  Stack,
  Collapse,
  Grid,
  IconButton,
  TextField,
  Typography,
  Select,
  MenuItem,
  Paper,
  useTheme,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { breakPointsMaxwidth } from '@/config/breakpoints';
import { useReactToPrint } from 'react-to-print';
import styled from 'styled-components';
import useSettings from '@/hooks/useSettings';
import { EXAM_SELECT_OPTIONS } from '@/config/Selection';
import TabButton from '@/components/shared/TabButton';
import holyLogo from '@/assets/SchoolLogos/StThereseLogo.png';

const A4Div = styled.div`
  width: 207mm;
  min-height: 297mm;
  border: 1.5px solid black;
  padding: 5mm;
  margin-bottom: 10mm;
  box-sizing: border-box;
  border-radius: 10px;
  page-break-before: always;
  page-break-after: always;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;
type StuddentWiseProgrssReport = {
  subject: string;
  semester1: string | null;
  semester2: string | null;
};

type AttendanceData = {
  month: string;
  workingDays: number | null;
  presentDays: number | null;
};

const dummyData: StuddentWiseProgrssReport[] = [
  { subject: 'TOTAL', semester1: null, semester2: null },
  { subject: 'ALGEBRA', semester1: 'E', semester2: null },
  { subject: 'GEOMETRY', semester1: 'C2', semester2: null },
  { subject: 'TOTAL (MATHS)', semester1: null, semester2: null },
  { subject: 'SCIENCE 1', semester1: 'E', semester2: null },
  { subject: 'SCIENCE 2', semester1: 'D', semester2: null },
  { subject: 'TOTAL (SCIENCE)', semester1: null, semester2: null },
  { subject: 'HISTORY & POLITICAL SC.', semester1: 'D', semester2: null },
  { subject: 'GEOGRAPHY', semester1: 'C1', semester2: null },
  { subject: 'TOTAL (SS)', semester1: null, semester2: null },
];

const attendanceData: AttendanceData[] = [
  { month: 'JUNE', workingDays: null, presentDays: null },
  { month: 'JULY', workingDays: null, presentDays: null },
  { month: 'AUG', workingDays: null, presentDays: null },
  { month: 'SEP', workingDays: null, presentDays: null },
  { month: 'OCT', workingDays: null, presentDays: null },
  { month: 'NOV', workingDays: null, presentDays: null },
  { month: 'DEC', workingDays: null, presentDays: null },
  { month: 'JAN', workingDays: null, presentDays: null },
  { month: 'FEB', workingDays: null, presentDays: null },
  { month: 'MAR', workingDays: null, presentDays: null },
  { month: 'APR', workingDays: null, presentDays: null },
];

const StThereseRoot = styled.div`
  padding: 1rem;

  .Card {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 100px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 100px);
      flex-grow: 1;
      .certificate {
        /* border: 2px solid ${(props) => props.theme.palette.primary.main}; */
        /* padding: 0px 20px; */
        /* border-radius: 10px; */
      }
      .main-card-container {
        /* flex-grow: 1;*/
        /* width: 100%;*/
        /* height: 100%;*/
        /* display: flex;*/
        /* flex-direction: column;*/
        /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter};*/
        /* border-radius: 6px;*/
        /* overflow: hidden;*/
        .card-top {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? props.theme.palette.primary.lighter : props.theme.palette.grey[900]};
        }

        .card-table-container {
          flex-grow: 1;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          /* border: 1px solid ${(props) => props.theme.palette.secondary.lighter}; */
          overflow: hidden;
          border-radius: 0px;

          .MuiTableContainer-root {
            height: 100%;
          }
          .MuiTable-root {
          }

          .MuiTablePagination-root {
            flex-grow: 1;
            flex-shrink: 0;
          }
        }
      }
      .avg_circle_icon {
        font-size: 10px;
        color: ${(props) => props.theme.palette.primary.light};
        margin-right: 3px;
      }
      @media ${breakPointsMaxwidth.xl} {
        .MuiTableCell-root {
          font-size: 11px;
        }
        .MuiTableCell-root .MuiTypography-root {
          font-size: 11px;
        }
      }
      @media screen and (max-width: 1217px) {
        .MuiFormControl-root {
          width: 200px;
        }
        .select_box {
          width: 200px;
        }
      }
      @media screen and (max-width: 1160px) {
        .MuiTableContainer-root {
          /* width: 900px; */
        }
        .card-table-container {
          overflow: auto;
        }
      }
    }
    .card_top {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
    }
    .title_searchbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
    }
    .progress_report_tab {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    @media screen and (max-width: 1160px) {
      .card_top {
        display: flex;
        flex-direction: column;
      }
      .progress_report_tab {
        display: flex;
        justify-content: end;
        padding-bottom: 10px;
        overflow-x: auto;
        ::-webkit-scrollbar {
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: ${(props) => props.theme.palette.grey[400]};
          border-radius: 20px;
        }
      }
    }
    @media screen and (max-width: 998px) {
      .progress_report_tab {
        justify-content: start;
      }
    }
    @media screen and (max-width: 768px) {
      .progress_report_tab {
        ::-webkit-scrollbar {
          height: 0px;
        }
      }
    }
  }
`;

export type StudentClassWiseProps = {
  onClickPromotionList: () => void;
  onClickClassWise: () => void;
  onClickTopper: () => void;
  onClickGradeWise: () => void;
};
function StTherese({ onClickPromotionList, onClickClassWise, onClickTopper, onClickGradeWise }: StudentClassWiseProps) {
  const [divCount, setDivCount] = useState(1);
  const [showFilter, setShowFilter] = useState(false);
  const theme = useTheme();
  const { themeMode } = useSettings();
  // const getRandomNumber = () => Math.floor(Math.random() * 1000000);

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Student_Wise_Progress_Report_StTherese_${new Date()
      .toLocaleString('en-GB')
      .replace(/\//g, '-')
      .replace(/:/g, '.')
      .replace(/, /g, '_')}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .MuiTableCell-root {
          border: 1px solid ${theme.palette.secondary.main} !important;
          height: 10px !important;
        }
        .MuiTableContainer-root {
          height: 100%;
        }
        .MuiTable-root {
        }
      }
    `,
  });

  const getRowKey = useCallback((row: StuddentWiseProgrssReport) => row.subject, []);
  const getRowKey2 = useCallback((row: AttendanceData) => row.month, []);

  const progressListColumns = useMemo(
    () => [
      {
        name: 'subject',
        dataKey: 'subject',
        headerLabel: 'Subjects',
      },
      {
        name: 'semester1',
        dataKey: 'semester1',
        headerLabel: 'Semester 1',
      },
      {
        name: 'semester2',
        dataKey: 'semester2',
        headerLabel: 'Semester 2',
      },
    ],
    []
  );

  const attendanceColumns = useMemo(
    () => [
      {
        name: 'month',
        dataKey: 'month',
        headerLabel: 'Month',
      },
      {
        name: 'workingDays',
        dataKey: 'workingDays',
        headerLabel: 'No. of Working Days',
      },
      {
        name: 'presentDays',
        dataKey: 'presentDays',
        headerLabel: 'No. of Days Present',
      },
    ],
    []
  );

  return (
    <Page title="Student Wise">
      <StThereseRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <div className="card_top ">
            <div className="title_searchbar">
              <Typography variant="h6" fontSize={17}>
                Student Individual
              </Typography>

              <Box sx={{ flexShrink: 0 }}>
                <IconButton aria-label="delete" color="primary" sx={{ mr: 2 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Box>
            </div>
            <div className="progress_report_tab">
              <div style={{ flexShrink: 0 }}>
                <TabButton title="Student Individual" variant="contained" />
                <TabButton title="Class Wise" variant="outlined" onClick={onClickClassWise} />
                <TabButton title="Student Promotion List" variant="outlined" onClick={onClickPromotionList} />
                <TabButton title="Topper" variant="outlined" onClick={onClickTopper} />
                <TabButton title="Grade Wise" variant="outlined" onClick={onClickGradeWise} />
              </div>
            </div>
          </div>
          <Divider sx={{ py: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Year
                      </Typography>
                      <TextField name="year" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Exam
                      </Typography>
                      <Select
                        sx={{ minWidth: { xs: '100%', xl: 240 } }}
                        className="select_box"
                        labelId="classStatusFilter"
                        id="classStatusFilterSelect"
                      >
                        <MenuItem value={-1}>All</MenuItem>
                        {EXAM_SELECT_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.exam}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', xl: 240 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <TextField name="className" sx={{ minWidth: { xs: '100%', xl: 240 } }} />
                    </FormControl>
                  </Grid>
                  <Grid item sm="auto" xs={12}>
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Box display="flex" justifyContent="center" alignItems="center">
              <Box ref={componentRef} sx={{ WebkitPrintColorAdjust: 'exact' }} className="main-card-container">
                {Array.from({ length: divCount }).map(() => (
                  <A4Div>
                    <Stack direction="row" justifyContent="center" alignItems="start" gap={2}>
                      <img src={holyLogo} width={60} alt="logo" />
                      <Stack direction="column" justifyContent="center" alignItems="center" gap={0.5}>
                        <Typography variant="h4" fontSize={27} fontWeight={700}>
                          ST.THERESE CONVENT HIGH SCHOOL
                        </Typography>
                        <Typography variant="h6" color="GrayText" fontSize={15}>
                          Kolegaon, Dombivli East, Maharashtra 421204
                        </Typography>
                        <Typography variant="h4" fontSize={22} pt={5} align="center">
                          PROGRESS REPORT 2023-2024
                        </Typography>
                      </Stack>
                    </Stack>
                    <Box
                      my={4}
                      sx={{ display: 'flex', justifyContent: { xs: 'start', sm: 'space-between' }, px: { xs: 3 } }}
                      flexWrap="wrap"
                    >
                      <Stack direction="column" gap={1}>
                        <Typography variant="h6" fontSize={14}>
                          Name: <span style={{ fontWeight: 'normal' }}>MUHAMMED ANSAR A</span>
                        </Typography>
                        <Typography variant="h6" fontSize={14}>
                          Class: <span style={{ fontWeight: 'normal' }}>I-A</span>
                        </Typography>
                      </Stack>
                      <Stack direction="column" gap={1}>
                        <Typography variant="h6" fontSize={14}>
                          Academic : <span style={{ fontWeight: 'normal' }}> 2023-2024</span>
                        </Typography>
                        <Typography variant="h6" fontSize={14}>
                          Assessment : <span style={{ fontWeight: 'normal' }}> Term-2</span>
                        </Typography>
                      </Stack>
                    </Box>
                    <Paper>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              {progressListColumns.map((column) => (
                                <TableCell
                                  key={column.dataKey}
                                  align="center"
                                  sx={{
                                    height: 10,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontWeight: 700,
                                    fontSize: 13,
                                  }}
                                >
                                  {column.headerLabel}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {dummyData.map((row) =>
                              !row.subject.includes('TOTAL') ? (
                                <TableRow key={getRowKey(row)}>
                                  {progressListColumns.map((column) => (
                                    <TableCell
                                      align={column.headerLabel !== 'Subjects' ? 'center' : 'left'}
                                      key={column.dataKey}
                                      sx={{
                                        height: 10,
                                        border: `1px solid ${theme.palette.secondary.light}`,
                                        fontWeight: 700,
                                        fontSize: 13,
                                      }}
                                    >
                                      {row[column.dataKey as keyof StuddentWiseProgrssReport]}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ) : (
                                <TableRow key={getRowKey(row)}>
                                  {progressListColumns.map(
                                    (column) =>
                                      column.headerLabel !== 'Semester 2' && (
                                        <TableCell
                                          key={column.dataKey}
                                          align={column.headerLabel !== 'Subjects' ? 'center' : 'left'}
                                          colSpan={column.headerLabel !== 'Semester 1' ? 1 : 2}
                                          sx={{
                                            height: 10,
                                            border: `1px solid ${theme.palette.secondary.light}`,
                                            fontWeight: 700,
                                            fontSize: 13,
                                          }}
                                        >
                                          {row[column.dataKey as keyof StuddentWiseProgrssReport]}
                                        </TableCell>
                                      )
                                  )}
                                </TableRow>
                              )
                            )}
                            <TableRow>
                              <TableCell colSpan={3} sx={{ height: 20, border: 'none' }} />
                            </TableRow>
                            <TableRow>
                              <TableCell
                                sx={{
                                  height: 10,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontWeight: 700,
                                }}
                              >
                                REMARKS
                              </TableCell>
                              <TableCell
                                colSpan={2}
                                sx={{ height: 10, border: `1px solid ${theme.palette.secondary.light}` }}
                              />
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                    <TableContainer>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableCell
                              sx={{
                                height: 10,
                                border: `1px solid ${theme.palette.secondary.light}`,
                                fontSize: 13,
                                fontWeight: 700,
                              }}
                            >
                              GRADE
                            </TableCell>
                            {[
                              'A1 : 100-91',
                              'A2 : 90-81',
                              'B1 : 80-71',
                              'B2 : 70-61',
                              'C1 : 60-51',
                              'C2 : 50-41',
                              'D : 40-32',
                              'Below-32 : E',
                            ].map((column) => (
                              <TableCell
                                align="center"
                                sx={{
                                  height: 10,
                                  border: `1px solid ${theme.palette.secondary.light}`,
                                  fontSize: 13,
                                  fontWeight: 700,
                                }}
                              >
                                {column}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                    <TableContainer>
                      <Typography variant="h6" align="center" fontSize={16} sx={{ py: 1 }}>
                        ATTENDANCE
                      </Typography>
                      <Table>
                        <TableRow>
                          <TableCell
                            align="center"
                            sx={{
                              height: 10,
                              border: `1px solid ${theme.palette.secondary.light}`,
                              fontWeight: 700,
                              fontSize: 13,
                            }}
                          >
                            Category
                          </TableCell>
                          {attendanceData.map((row) => (
                            <TableCell
                              key={getRowKey2(row)}
                              align="center"
                              sx={{
                                height: 10,
                                border: `1px solid ${theme.palette.secondary.light}`,
                                fontWeight: 700,
                                fontSize: 13,
                              }}
                            >
                              {row.month}
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableBody>
                          {attendanceColumns.map((column) =>
                            column.dataKey !== 'month' ? (
                              <TableRow key={column.dataKey}>
                                <TableCell
                                  align="center"
                                  sx={{
                                    height: 10,
                                    border: `1px solid ${theme.palette.secondary.light}`,
                                    fontWeight: 700,
                                    fontSize: 13,
                                  }}
                                >
                                  {column.headerLabel}
                                </TableCell>
                                {attendanceData.map((row) => (
                                  <TableCell
                                    key={`${row.month}-${column.dataKey}`}
                                    align="center"
                                    sx={{
                                      height: 10,
                                      border: `1px solid ${theme.palette.secondary.light}`,
                                      fontWeight: 700,
                                      fontSize: 13,
                                    }}
                                  >
                                    {row[column.dataKey as keyof AttendanceData] === null
                                      ? '__'
                                      : row[column.dataKey as keyof AttendanceData]}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ) : null
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    <Stack direction="row" gap={1} pt={10} justifyContent="space-around">
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        CLASS TEACHER&apos;S SIGN
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        PARENT&apos;S SIGN
                      </Typography>
                      <Typography variant="h5" fontSize={12} fontWeight={700}>
                        PRINCIPAL&apos;S SIGN
                      </Typography>
                    </Stack>
                  </A4Div>
                ))}
              </Box>
            </Box>
          </div>

          <Box display="flex" sx={{ justifyContent: { xs: 'center', md: 'right' }, pr: { lg: '5' }, mt: 3 }}>
            <Stack spacing={2} direction="row">
              <Box>
                <Button
                  variant="contained"
                  disabled={divCount === 0}
                  onClick={() => setDivCount(divCount - 1)}
                  sx={{ ml: 2 }}
                >
                  -
                </Button>
                <TextField
                  type="number"
                  value={divCount}
                  onChange={(e) => setDivCount(Number(e.target.value))}
                  sx={{ mx: 2 }}
                />
                <Button variant="contained" onClick={() => setDivCount(divCount + 1)}>
                  +
                </Button>
                <Button variant="contained" color="primary" onClick={handlePrint} sx={{ ml: 2 }}>
                  Print
                </Button>
              </Box>
            </Stack>
          </Box>
        </Card>
      </StThereseRoot>
    </Page>
  );
}

export default StTherese;
