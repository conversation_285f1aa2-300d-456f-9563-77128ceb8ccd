import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { AdminControlsState, AdminLoginDataType } from '@/types/AdminControls';
import { flushStore } from '../flush.slice';
import { fetchAdminLoginList, fetchParentLoginList } from './adminControls.thunks';

const initialState: AdminControlsState = {
  manageLoginList: {
    data: [],
    status: 'idle',
    error: null,
  },
   parentLoginList: {
    data: [],
    status: 'idle',
    error: null,
  },
  submitting: false,
  deletingRecords: {},
  error: null,
};

export const adminControlsSlice = createSlice({
  name: 'adminControls',
  initialState,
  reducers: {
    setManageLoginList: (state, action: PayloadAction<AdminLoginDataType[]>) => {
      state.manageLoginList.data = action.payload;
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.submitting = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      // extraReducers for fetching manage login
      // ------ Get Admin Login List------- //
      .addCase(fetchAdminLoginList.pending, (state) => {
        state.manageLoginList.status = 'loading';
        state.manageLoginList.error = null;
      })
      .addCase(fetchAdminLoginList.fulfilled, (state, action) => {
        state.manageLoginList.status = 'success';
        state.manageLoginList.data = action.payload;
        state.manageLoginList.error = null;
      })
      .addCase(fetchAdminLoginList.rejected, (state, action) => {
        state.manageLoginList.status = 'error';
        state.manageLoginList.error = action.payload || 'Unknown error in fetching Manage Login';
      })

      // ------ Get Parent Login List------- //
      .addCase(fetchParentLoginList.pending, (state) => {
        state.parentLoginList.status = 'loading';
        state.parentLoginList.error = null;
      })
      .addCase(fetchParentLoginList.fulfilled, (state, action) => {
        state.parentLoginList.status = 'success';
        state.parentLoginList.data = action.payload;
        state.parentLoginList.error = null;
      })
      .addCase(fetchParentLoginList.rejected, (state, action) => {
        state.parentLoginList.status = 'error';
        state.parentLoginList.error = action.payload || 'Unknown error in fetching Manage Login';
      })

      .addCase(flushStore, () => initialState);
  },
});

export const { setManageLoginList } = adminControlsSlice.actions;

export default adminControlsSlice.reducer;
