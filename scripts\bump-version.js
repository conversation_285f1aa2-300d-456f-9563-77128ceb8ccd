// const fs = require('fs');
// const path = require('path');

// const versionFile = path.join(__dirname, '../version.json');

// // Read version.json
// const data = JSON.parse(fs.readFileSync(versionFile, 'utf8'));
// let [major, minor, patch] = data.version.split('.').map(Number);

// // Increment patch
// patch++;

// // New version
// const newVersion = `${major}.${minor}.${patch}`;
// data.version = newVersion;

// // Save new version
// fs.writeFileSync(versionFile, JSON.stringify(data, null, 2));

// console.log('New version:', newVersion);


// const fs = require('fs');
// const path = require('path');
// import fs from 'fs';
// import path from 'path';
// import { fileURLToPath } from 'url';

// // ---- FIX __dirname in ESM ----
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
// // --------------------------------

// const versionFile = path.join(__dirname, '../version.json');

// // Remove v and bump patch
// function bumpPatchVersion(v) {
//   const clean = v.startsWith('v') ? v.substring(1) : v; // remove "v"

//   const parts = clean.split('.').map((p) => parseInt(p, 10));
//   if (parts.length !== 3 || parts.some(isNaN)) {
//     throw new Error('version.json version is not semver (x.y.z)');
//   }

//   parts[2] = parts[2] + 1;
//   return `v${parts.join('.')}`; // add "v" back
// }

// try {
//   const raw = fs.readFileSync(versionFile, 'utf8');
//   const json = JSON.parse(raw);

//   const current = String(json.version || 'v0.0.0');
//   const next = bumpPatchVersion(current);

//   json.version = next;

//   fs.writeFileSync(versionFile, JSON.stringify(json, null, 2), 'utf8');
//   console.log(`version bumped: ${current} -> ${next}`);
// } catch (err) {
//   console.error('Failed to bump version:', err);
//   process.exit(1);
// }
// ==============================================================================//
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ---- Fix __dirname in ESM ----
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// --------------------------------

const versionFile = path.join(__dirname, '../version.json');

function bumpCustomVersion(version) {
  // Remove "v" if exists
  const clean = version.startsWith('v') ? version.slice(1) : version;

  let [major, minor, patch] = clean.split('.').map(Number);

  if ([major, minor, patch].some(isNaN)) {
    throw new Error('Invalid version format (must be vX.Y.Z)');
  }

  // ----- Your custom logic -----
  if (patch < 9) {
    patch += 1;
  } else {
    // patch reached 9 → reset and bump minor
    patch = 0;
    minor += 1;
  }
  // -----------------------------

  return `v${major}.${minor}.${patch}`;
}

try {
  const raw = fs.readFileSync(versionFile, 'utf8');
  const json = JSON.parse(raw);

  const current = json.version || 'v0.0.0';
  const next = bumpCustomVersion(current);

  json.version = next;

  fs.writeFileSync(versionFile, JSON.stringify(json, null, 2), 'utf8');

  console.log(`Version bumped: ${current} → ${next}`);
} catch (err) {
  console.error('Failed to bump version:', err);
  process.exit(1);
}


// import fs from "fs";
// import path from "path";
// import { fileURLToPath } from "url";

// // Fix __dirname in ESM
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

// // -----------------------------
// // 1) Always use local mode
// // -----------------------------
// const mode = "local";
// console.log("🏫 BUILD MODE =", mode);

// // -----------------------------
// // 2) Load .env.local
// // -----------------------------
// const envFile = path.join(__dirname, "../.env.local");

// if (!fs.existsSync(envFile)) {
//   console.error(`❌ ERROR: env file not found => ${envFile}`);
//   process.exit(1);
// }

// console.log(`📄 Using env file: ${envFile}`);

// // Read env file
// const envContent = fs.readFileSync(envFile, "utf8");
// const envLines = envContent.split(/\r?\n/);

// let schoolIndex = "0"; // default school index
// for (const line of envLines) {
//   if (line.startsWith("VITE_SCHOOL_INDEX")) {
//     const [, value] = line.split("=");
//     if (value !== undefined) {
//       schoolIndex = value.trim();
//       break;
//     }
//   }
// }

// console.log("🏫 Using School Index =", schoolIndex);

// // -----------------------------
// // 3) Version bump function
// // -----------------------------
// function bumpVersion(version) {
//   const clean = version.startsWith("v") ? version.slice(1) : version;
//   let [major, minor, patch] = clean.split(".").map(Number);

//   if ([major, minor, patch].some(isNaN)) {
//     throw new Error("Invalid version format: " + version);
//   }

//   if (patch < 9) patch++;
//   else {
//     patch = 0;
//     minor++;
//   }

//   return `v${major}.${minor}.${patch}`;
// }

// // -----------------------------
// // 4) Update version.json
// // -----------------------------
// const versionFile = path.join(__dirname, "../version.json");

// try {
//   if (!fs.existsSync(versionFile)) {
//     console.warn("⚠ version.json not found. Creating a new one...");
//     fs.writeFileSync(versionFile, JSON.stringify({}, null, 2));
//   }

//   const raw = fs.readFileSync(versionFile, "utf8");
//   const json = raw ? JSON.parse(raw) : {};

//   const current = json[schoolIndex] || "v0.0.0";
//   const next = bumpVersion(current);

//   // Update only this school
//   json[schoolIndex] = next;

//   fs.writeFileSync(versionFile, JSON.stringify(json, null, 2));

//   console.log(`✅ School ${schoolIndex}: ${current} → ${next}`);
// } catch (err) {
//   console.error(err);
//   process.exit(1);
// }


// ==============================================================//
// import fs from "fs"; 
// import path from "path";
// import { fileURLToPath } from "url";
// import dotenv from "dotenv";

// // Fix __dirname in ESM
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

// // -------------------------
// // Get MODE from argv or npm_config_mode
// // -------------------------
// let mode = "local"; // default

// const modeArg = process.argv[2]; // e.g., "mim"
// if (modeArg) mode = modeArg;

// if (process.env.npm_config_mode) mode = process.env.npm_config_mode;

// console.log(`📌 BUILD MODE: ${mode}`);

// // -------------------------
// // Load .env.<mode>
// // -------------------------
// const envFile = path.resolve(__dirname, `../.env.${mode}`);
// if (!fs.existsSync(envFile)) {
//   console.error(`❌ ERROR: Cannot find file .env.${mode}`);
//   process.exit(1);
// }
// dotenv.config({ path: envFile });
// console.log(`📄 Loaded: ${envFile}`);

// // -------------------------
// // Read school index
// // -------------------------
// const schoolIndex = Number(process.env.VITE_SCHOOL_INDEX);
// if (isNaN(schoolIndex)) {
//   console.error(`❌ ERROR: VITE_SCHOOL_INDEX is missing or invalid in .env.${mode}`);
//   process.exit(1);
// }
// console.log(`🏫 SCHOOL INDEX: ${schoolIndex}`);

// // -------------------------
// // Version bump
// // -------------------------
// function bumpVersion(version) {
//   const clean = version.replace(/^v/, "");
//   let [major, minor, patch] = clean.split(".").map(Number);

//   if (patch < 9) patch++;
//   else { patch = 0; minor++; }

//   return `v${major}.${minor}.${patch}`;
// }

// // -------------------------
// // Update version.json
// // -------------------------
// const versionFile = path.resolve(__dirname, "../version.json");
// let json = {};

// // Create default if missing
// if (!fs.existsSync(versionFile)) {
//   for (let i = 0; i < 10; i++) json[i] = "v1.0.0";
//   fs.writeFileSync(versionFile, JSON.stringify(json, null, 2));
// }

// // Read existing
// json = JSON.parse(fs.readFileSync(versionFile, "utf8"));

// // Ensure all indices exist
// for (let i = 0; i < 10; i++) if (!json[i]) json[i] = "v1.0.0";

// const current = json[schoolIndex];
// const next = bumpVersion(current);

// json[schoolIndex] = next;
// fs.writeFileSync(versionFile, JSON.stringify(json, null, 2));

// console.log(`✅ Version updated: school ${schoolIndex} ${current} → ${next}`);
